#!/bin/bash

# SMS Reminder System AWS Deployment Script
# This script sets up all AWS resources needed for the SMS reminder system
# Run this from your Mac terminal after configuring AWS CLI

set -e  # Exit on any error

# Configuration variables - UPDATE THESE FOR YOUR ENVIRONMENT
AWS_REGION="us-east-1"
ENVIRONMENT="prod"  # Change to "dev" or "staging" as needed
PROJECT_NAME="hmbl-sms-reminders"

# Derived names
LAMBDA_FUNCTION_NAME="${PROJECT_NAME}-handler-${ENVIRONMENT}"
SCHEDULE_GROUP_NAME="${PROJECT_NAME}-${ENVIRONMENT}"
LAMBDA_ROLE_NAME="${PROJECT_NAME}-lambda-role-${ENVIRONMENT}"
SCHEDULER_ROLE_NAME="${PROJECT_NAME}-scheduler-role-${ENVIRONMENT}"
LOG_GROUP_NAME="/aws/lambda/${LAMBDA_FUNCTION_NAME}"

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Helper functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check prerequisites
check_prerequisites() {
    log_info "Checking prerequisites..."
    
    # Check if AWS CLI is installed
    if ! command -v aws &> /dev/null; then
        log_error "AWS CLI is not installed. Please install it first:"
        echo "brew install awscli"
        exit 1
    fi
    
    # Check if AWS CLI is configured
    if ! aws sts get-caller-identity &> /dev/null; then
        log_error "AWS CLI is not configured. Please run 'aws configure' first."
        exit 1
    fi
    
    # Check if zip is available
    if ! command -v zip &> /dev/null; then
        log_error "zip command is not available."
        exit 1
    fi
    
    # Get AWS account ID
    AWS_ACCOUNT_ID=$(aws sts get-caller-identity --query Account --output text)
    log_success "AWS Account ID: $AWS_ACCOUNT_ID"
    log_success "AWS Region: $AWS_REGION"
}

# Create IAM roles
create_iam_roles() {
    log_info "Creating IAM roles..."
    
    # Lambda execution role trust policy
    cat > /tmp/lambda-trust-policy.json << EOF
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Principal": {
        "Service": "lambda.amazonaws.com"
      },
      "Action": "sts:AssumeRole"
    }
  ]
}
EOF

    # Lambda execution role policy
    cat > /tmp/lambda-execution-policy.json << EOF
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Action": [
        "logs:CreateLogGroup",
        "logs:CreateLogStream",
        "logs:PutLogEvents"
      ],
      "Resource": "arn:aws:logs:${AWS_REGION}:${AWS_ACCOUNT_ID}:*"
    },
    {
      "Effect": "Allow",
      "Action": [
        "cloudwatch:PutMetricData"
      ],
      "Resource": "*"
    }
  ]
}
EOF

    # Create Lambda execution role
    if aws iam get-role --role-name "$LAMBDA_ROLE_NAME" &> /dev/null; then
        log_warning "Lambda role $LAMBDA_ROLE_NAME already exists"
    else
        aws iam create-role \
            --role-name "$LAMBDA_ROLE_NAME" \
            --assume-role-policy-document file:///tmp/lambda-trust-policy.json \
            --description "Execution role for SMS reminder Lambda function"
        
        aws iam put-role-policy \
            --role-name "$LAMBDA_ROLE_NAME" \
            --policy-name "LambdaExecutionPolicy" \
            --policy-document file:///tmp/lambda-execution-policy.json
        
        log_success "Created Lambda execution role: $LAMBDA_ROLE_NAME"
    fi

    # EventBridge Scheduler role trust policy
    cat > /tmp/scheduler-trust-policy.json << EOF
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Principal": {
        "Service": "scheduler.amazonaws.com"
      },
      "Action": "sts:AssumeRole"
    }
  ]
}
EOF

    # EventBridge Scheduler role policy
    cat > /tmp/scheduler-execution-policy.json << EOF
{
  "Version": "2012-10-17",
  "Statement": [
    {
      "Effect": "Allow",
      "Action": [
        "lambda:InvokeFunction"
      ],
      "Resource": "arn:aws:lambda:${AWS_REGION}:${AWS_ACCOUNT_ID}:function:${LAMBDA_FUNCTION_NAME}*"
    }
  ]
}
EOF

    # Create EventBridge Scheduler role
    if aws iam get-role --role-name "$SCHEDULER_ROLE_NAME" &> /dev/null; then
        log_warning "Scheduler role $SCHEDULER_ROLE_NAME already exists"
    else
        aws iam create-role \
            --role-name "$SCHEDULER_ROLE_NAME" \
            --assume-role-policy-document file:///tmp/scheduler-trust-policy.json \
            --description "Execution role for EventBridge Scheduler to invoke Lambda"
        
        aws iam put-role-policy \
            --role-name "$SCHEDULER_ROLE_NAME" \
            --policy-name "SchedulerExecutionPolicy" \
            --policy-document file:///tmp/scheduler-execution-policy.json
        
        log_success "Created EventBridge Scheduler role: $SCHEDULER_ROLE_NAME"
    fi

    # Wait for roles to be available
    log_info "Waiting for IAM roles to be available..."
    sleep 10
}

# Create CloudWatch Log Group
create_log_group() {
    log_info "Creating CloudWatch Log Group..."
    
    if aws logs describe-log-groups --log-group-name-prefix "$LOG_GROUP_NAME" --query 'logGroups[?logGroupName==`'$LOG_GROUP_NAME'`]' --output text | grep -q "$LOG_GROUP_NAME"; then
        log_warning "Log group $LOG_GROUP_NAME already exists"
    else
        aws logs create-log-group --log-group-name "$LOG_GROUP_NAME"
        aws logs put-retention-policy --log-group-name "$LOG_GROUP_NAME" --retention-in-days 30
        log_success "Created CloudWatch Log Group: $LOG_GROUP_NAME"
    fi
}

# Package and deploy Lambda function
deploy_lambda() {
    log_info "Packaging and deploying Lambda function..."
    
    # Check if Lambda directory exists
    if [ ! -d "lambda/sms-reminder-handler" ]; then
        log_error "Lambda function directory not found. Please run this script from the project root."
        exit 1
    fi
    
    # Package Lambda function
    cd lambda/sms-reminder-handler
    zip -r /tmp/${LAMBDA_FUNCTION_NAME}.zip index.js package.json
    cd - > /dev/null
    
    # Get Lambda role ARN
    LAMBDA_ROLE_ARN=$(aws iam get-role --role-name "$LAMBDA_ROLE_NAME" --query 'Role.Arn' --output text)
    
    # Create or update Lambda function
    if aws lambda get-function --function-name "$LAMBDA_FUNCTION_NAME" &> /dev/null; then
        log_info "Updating existing Lambda function..."
        aws lambda update-function-code \
            --function-name "$LAMBDA_FUNCTION_NAME" \
            --zip-file fileb:///tmp/${LAMBDA_FUNCTION_NAME}.zip
    else
        log_info "Creating new Lambda function..."
        aws lambda create-function \
            --function-name "$LAMBDA_FUNCTION_NAME" \
            --runtime nodejs18.x \
            --role "$LAMBDA_ROLE_ARN" \
            --handler index.handler \
            --zip-file fileb:///tmp/${LAMBDA_FUNCTION_NAME}.zip \
            --timeout 30 \
            --memory-size 128 \
            --description "SMS reminder handler for HMBL healthcare platform"
    fi
    
    # Set environment variables using a temporary file to avoid quoting issues
    cat > /tmp/lambda-env-vars.json << 'EOF'
{
  "Variables": {
    "TWILIO_ACCOUNT_SID": "**********************************",
    "TWILIO_AUTH_TOKEN": "eb227323ff58ff56eddb2ee41548754d",
    "TWILIO_PHONE_NUMBER": "+***********"
  }
}
EOF

    aws lambda update-function-configuration \
        --function-name "$LAMBDA_FUNCTION_NAME" \
        --environment file:///tmp/lambda-env-vars.json
    
    log_success "Lambda function deployed: $LAMBDA_FUNCTION_NAME"
}

# Create EventBridge Scheduler Group
create_scheduler_group() {
    log_info "Creating EventBridge Scheduler Group..."
    
    if aws scheduler get-schedule-group --name "$SCHEDULE_GROUP_NAME" &> /dev/null; then
        log_warning "Schedule group $SCHEDULE_GROUP_NAME already exists"
    else
        aws scheduler create-schedule-group \
            --name "$SCHEDULE_GROUP_NAME" \
            --description "Schedule group for SMS reminders - $ENVIRONMENT environment"
        
        log_success "Created EventBridge Scheduler Group: $SCHEDULE_GROUP_NAME"
    fi
}

# Create CloudWatch Alarms
create_cloudwatch_alarms() {
    log_info "Creating CloudWatch Alarms..."
    
    # Lambda error alarm
    aws cloudwatch put-metric-alarm \
        --alarm-name "${PROJECT_NAME}-lambda-errors-${ENVIRONMENT}" \
        --alarm-description "Alert on SMS reminder Lambda errors" \
        --metric-name Errors \
        --namespace AWS/Lambda \
        --statistic Sum \
        --period 300 \
        --threshold 5 \
        --comparison-operator GreaterThanThreshold \
        --dimensions Name=FunctionName,Value="$LAMBDA_FUNCTION_NAME" \
        --evaluation-periods 1 \
        --treat-missing-data notBreaching
    
    # Lambda duration alarm
    aws cloudwatch put-metric-alarm \
        --alarm-name "${PROJECT_NAME}-lambda-duration-${ENVIRONMENT}" \
        --alarm-description "Alert on SMS reminder Lambda high duration" \
        --metric-name Duration \
        --namespace AWS/Lambda \
        --statistic Average \
        --period 300 \
        --threshold 15000 \
        --comparison-operator GreaterThanThreshold \
        --dimensions Name=FunctionName,Value="$LAMBDA_FUNCTION_NAME" \
        --evaluation-periods 2 \
        --treat-missing-data notBreaching
    
    log_success "Created CloudWatch Alarms"
}

# Test Lambda function
test_lambda() {
    log_info "Testing Lambda function..."
    
    # Create test payload
    cat > /tmp/test-payload.json << EOF
{
  "phoneNumber": "+15551234567",
  "message": "Test SMS reminder from HMBL system - deployment verification",
  "reminderType": "test"
}
EOF

    # Invoke Lambda function
    aws lambda invoke \
        --function-name "$LAMBDA_FUNCTION_NAME" \
        --payload file:///tmp/test-payload.json \
        /tmp/lambda-response.json
    
    # Check response
    if grep -q '"success":true' /tmp/lambda-response.json; then
        log_success "Lambda function test passed"
    else
        log_warning "Lambda function test may have failed. Check response:"
        cat /tmp/lambda-response.json
    fi
}

# Generate environment variables
generate_env_vars() {
    log_info "Generating environment variables for your .env file..."
    
    LAMBDA_ARN=$(aws lambda get-function --function-name "$LAMBDA_FUNCTION_NAME" --query 'Configuration.FunctionArn' --output text)
    SCHEDULER_ROLE_ARN=$(aws iam get-role --role-name "$SCHEDULER_ROLE_NAME" --query 'Role.Arn' --output text)
    
    echo ""
    echo "=========================================="
    echo "Add these variables to your .env file:"
    echo "=========================================="
    echo "SMS_REMINDER_LAMBDA_ARN=$LAMBDA_ARN"
    echo "SMS_REMINDER_EXECUTION_ROLE_ARN=$SCHEDULER_ROLE_ARN"
    echo "SMS_REMINDER_SCHEDULE_GROUP=$SCHEDULE_GROUP_NAME"
    echo "SMS_REMINDER_CLOUDWATCH_NAMESPACE=HMBL/SMSReminders"
    echo "SMS_REMINDER_LOG_GROUP=$LOG_GROUP_NAME"
    echo "=========================================="
    echo ""
}

# Cleanup temporary files
cleanup() {
    rm -f /tmp/lambda-trust-policy.json
    rm -f /tmp/lambda-execution-policy.json
    rm -f /tmp/scheduler-trust-policy.json
    rm -f /tmp/scheduler-execution-policy.json
    rm -f /tmp/${LAMBDA_FUNCTION_NAME}.zip
    rm -f /tmp/test-payload.json
    rm -f /tmp/lambda-response.json
    rm -f /tmp/lambda-env-vars.json
}

# Main execution
main() {
    log_info "Starting SMS Reminder System AWS Deployment"
    log_info "Environment: $ENVIRONMENT"
    log_info "Region: $AWS_REGION"
    echo ""
    
    check_prerequisites
    create_iam_roles
    create_log_group
    deploy_lambda
    create_scheduler_group
    create_cloudwatch_alarms
    test_lambda
    generate_env_vars
    cleanup
    
    log_success "SMS Reminder System deployment completed successfully!"
    log_info "Next steps:"
    echo "1. Update your .env file with the generated environment variables"
    echo "2. Restart your Vapor application"
    echo "3. Test the system by creating an appointment or task"
    echo "4. Monitor CloudWatch logs and metrics"
}

# Run main function
main "$@"
