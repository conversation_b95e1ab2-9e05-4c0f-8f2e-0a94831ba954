//
//  File.swift
//  
//
//  Created by <PERSON> on 8/6/23.
//

import Foundation
import Vapor
import Fluent

struct AppointmentUpdateInput: Content {
    var title:        String?
    var status:       String?
    var kind:         String?
    var desc:         String?
    var scheduleAt:   String?
    var timeZone:     String?
    var duration:     String?
    var hostLink:     String?
    var meetingLink:  String?
    var meta:         MetaData? //may need to store zoom id for update
    var rate:         String?
    
    func returnUpdatedModel(appointment:Appointment) -> Appointment {
        
        if let title = title {
            appointment.title = title
        }
        if let status = status {
            appointment.status = status
        }
        if let kind = kind {
            appointment.kind = kind
        }
        if let desc = desc {
            appointment.desc = desc
        }
        if let scheduleAt = scheduleAt {
            appointment.scheduleAt = scheduleAt
        }
        if let timeZone = timeZone {
            appointment.timeZone = timeZone
        }
        if let duration = duration {
            appointment.duration = duration
        }
        if let hostLink = hostLink {
            appointment.hostLink = hostLink
        }
        if let meetingLink = meetingLink {
            appointment.meetingLink = meetingLink
        }
        if let meta = meta {
            appointment.meta = meta
        }
        
        if let rate = rate {
            appointment.rate = rate
        }
        
        return appointment
    }
}

struct AppointmentInput: Content {
    var creatorID:    String //USER or navigator
    var title:        String
    var status:       String
    var kind:         String
    var desc:         String
    var scheduleAt:   String?
    var scheduleEpoc: Int?
    var timezone:     String? //picked from constants
    var duration:     String
    var hostLink:     String?
    var meetingLink:  String?
    var memberID:     String
    var orgID:        String //we could add integrations into orgs so if one or wants zoom and the other wants twilio we can support.
    var meta:         MetaData?
    var memberBooking: Bool
    var networkId:     UUID?
    var taskId:        UUID?
    var rate:          String?
    
    func isVirtual() -> Bool {
        return kind.lowercased() == "virtual"
    }
    
    func zoomMeeting() -> ZoomMeeting {
        return ZoomMeeting(
            topic: title,
            type: 2,
            start_time: scheduleAt,
            duration: duration,
            timezone: timezone,
            agenda: desc,
            recurrence: defaultMeetingRecurrence,
            settings: defaultMeetingSettings)
    }
}

protocol ScheduleItem: Codable {
    var timeString: String { get }
    var dateString: String { get }
    var isCompleted: Bool { get }
    var isUrgent: Bool { get }
    var timestamp: Date { get }
    var patient: String? { get }
    var itemType: ItemType { get }
    var referenceId: UUID? { get }
    var data: MetaData? { get }
    var itemStatus: String? { get }
}


enum ItemType: String, Codable {
    case task
    case appointment
}

extension Appointment: ScheduleItem {
        
    var patient: String? {
        self.member?.fullName()
    }
        
    var timeString: String {
        self.formatScheduleEpoch()?.timeString ?? ""
    }
    
    var dateString: String {
        self.formatScheduleEpoch()?.dateString ?? ""
    }
    
    var isCompleted: Bool {
        self.status == "completed" || self.status == "complete"
    }
    
    var timestamp: Date {
        Date(timeIntervalSince1970: Double(self.scheduleEpoc ?? 0))
    }
    
    var itemType: ItemType {
        .appointment
    }
    
    var referenceId: UUID? {
        UUID(uuidString: self.id?.uuidString ?? "")
    }
    
    var data: MetaData? {
        self.meta
    }
    
    var itemStatus: String? {
        self.status
    }
    
    //Appointments dont have urgent status for now.
    var isUrgent: Bool {
        false
    }
        
}

extension TaskModel: ScheduleItem {
    
    var patient: String? {
        self.receivers.last?.fullName()
    }
    
    var timeString: String {
        guard let epochTime = self.dueAtEpoc else { return "" }
        return epochTime.formatScheduleEpoch()?.timeString ?? ""
    }
    
    var dateString: String {
        guard let epochTime = self.dueAtEpoc else { return "" }
        return epochTime.formatScheduleEpoch()?.dateString ?? ""
    }
    
    var isCompleted: Bool {
        self.status == "completed" || self.status == "complete"
    }
    
    var timestamp: Date {
        Date(timeIntervalSince1970: Double(self.dueAtEpoc ?? 0))
    }
    
    var itemType: ItemType {
        .task
    }
    
    var referenceId: UUID? {
        UUID(uuidString: self.id?.uuidString ?? "")
    }
    
    var data: MetaData? {
        self.meta
    }
    
    var itemStatus: String? {
        self.status
    }
    
    var isUrgent: Bool {
        urgent ?? false
    }
}


// Response models
struct ItemResponse: Content {
    var id: UUID
    var referenceId: UUID?
    var time: String
    var date: String
    var title: String
    var type: ItemType
    var isCompleted: Bool
    var urgent: Bool
    var patient: String?
    var meta: MetaData?
    var status: String?
    
    init(from item: any ScheduleItem, title: String, member: Member? = nil) {
        self.id = UUID()
        self.referenceId = item.referenceId
        self.time = item.timeString
        self.date = item.dateString
        self.title = title
        self.type = item.itemType
        self.isCompleted = item.isCompleted
        self.patient = item.patient ?? member?.fullName()
        self.meta = item.data
        self.status = item.itemStatus
        self.urgent = item.isUrgent
    }
}

struct DateGroup: Content {
    var date: String
    var items: [ItemResponse]
}

struct ScheduleDataResponse: Content {
    var active: [DateGroup]
    var completed: [DateGroup]
}


struct AppointmentsController: RouteCollection {
    
    func boot(routes: RoutesBuilder) throws {
        let appts = routes.grouped("appointments")
        appts.get(use: index)
        appts.get(":aptID", use: apptById)
        appts.post(use: create)
        appts.put(":aptID", use: update)
        appts.delete(":aptID", use: delete)
    }
    
    func apptById(req: Request) throws -> EventLoopFuture<Appointment> {
        guard let id = req.parameters.get("aptID") else { throw NetworkError.error(type: .appointments) }
        return try AppointmentsController.find(req: req, id: id)
    }
        
    func index(req: Request) throws -> EventLoopFuture<Page<Appointment>> {
        return self.buildQuery(query: Appointment.query(on: req.db), req: req).paginate(for: req)
    }
    
//    func apptById(req: Request) throws -> EventLoopFuture<Appointment> {
//        guard let id = req.parameters.get("aptID") else { throw NetworkError.error(type: .appointments) }
//        return try AppointmentsController.find(req: req, id: id)
//    }
    
    func update(req: Request) throws -> EventLoopFuture<Appointment> {
        guard let id = req.parameters.get("aptID") else { throw NetworkError.error(type: .appointments) }
        let input = try req.content.decode(AppointmentUpdateInput.self)
        let isCancelled = input.status == "cancel" || input.status == "canceled"
        let isCompleted = input.status == "complete" || input.status == "completed"

        return try AppointmentsController.find(req: req, id: id).flatMap { appointment in
            let updatedAppointment = input.returnUpdatedModel(appointment: appointment)
            return updatedAppointment.update(on: req.db).transform(to: updatedAppointment).flatMap { appt in

                // Cancel reminders if appointment is cancelled or completed
                let cancelReminders = (isCancelled || isCompleted) ?
                    cancelAppointmentReminders(req: req, appointmentId: appt.id!) :
                    req.eventLoop.makeSucceededFuture(())

                return cancelReminders.flatMap { _ in
                    if isCancelled {
                        return try! AuthController.userIdFromToken(req: req).flatMap { navId in
                            return try! cancelAppointmentTimelineItem(req: req,
                                                                     appt: appointment,
                                                                     navId: navId).transform(to: appointment)
                        }
                    } else {
                        return req.eventLoop.future(appt)
                    }
                }
            }
        }
    }
        
    func create(req: Request) throws -> EventLoopFuture<Appointment> {
        let input = try req.content.decode(AppointmentInput.self)
        
        if input.isVirtual() {
            return try createZoomMeeting(req: req, input: input)
        } else {
            return try createStandardAppointment(req: req, input: input)
        }
    }
    
    static func createAppointment(req: Request, input: AppointmentInput, hostLink:String? = nil, meetingLink:String? = nil)  throws -> EventLoopFuture<Appointment> {
        return try AppointmentsController().createStandardAppointment(req: req, input: input)
    }
    
    fileprivate func createStandardAppointment(req: Request, input: AppointmentInput, hostLink:String? = nil, meetingLink:String? = nil)  throws -> EventLoopFuture<Appointment> {

        return try! OrgsController.find(req: req, id: input.orgID).flatMap { org in
            
            return try! MembersController.find(req: req, id: input.memberID).flatMap{ member in
                
                let appointment = Appointment(creatorID: input.creatorID,
                                              memberID: member.id?.uuidString ?? "",
                                              title: input.title,
                                              status: input.status,
                                              kind: input.kind,
                                              desc: input.desc,
                                              scheduleAt: input.scheduleAt,
                                              scheduleEpoc: input.scheduleEpoc,
                                              timeZone: input.timezone,
                                              duration: input.duration,
                                              hostLink: hostLink,
                                              meetingLink: meetingLink,
                                              meta: input.meta,
                                              memberBook: input.memberBooking,
                                              networkId: input.networkId,
                                              taskId: input.taskId,
                                              rate: input.rate)
                
                return org.$appointments.create(appointment, on: req.db).transform(to: appointment).flatMap { createdApt in

                    return try! AppointmentsController.find(req: req, id: createdApt.id?.uuidString ?? "").flatMap { appt in

                        appt.$member.id = member.id

                        if input.isVirtual() {

                            return try! UsersController.find(req: req, id: input.creatorID).flatMap { creator in

                                appt.$host.id = creator.id

                                return appt.update(on: req.db).transform(to: appt).flatMap { appointmentSaved in

                                    // Schedule SMS reminders for the appointment
                                    return scheduleAppointmentReminders(req: req, appointment: appointmentSaved, member: member).flatMap { _ in
                                        return req.eventLoop.future(appointmentSaved)
                                    }
                                    
                                    return req.eventLoop.future(appointmentSaved)
//                                    if input.isVirtual(),
//                                        let smsPhone = member.smsPhone(),
//                                        let link = meetingLink,
//                                        let dateTime = appointmentSaved.formattedDateAndTime() {
//                                        return req.eventLoop.future(appointmentSaved)
//        //                                    return try! TwilioController.sendSmsMessage(req: req, input: ZoomController.meetingMessage(phone: smsPhone, name: member.firstName, navigator: creator.memberInfo().fullName, url: link, date: dateTime)).flatMap{ reponse in
//        ////                                        print(reponse)
//        //                                        return req.eventLoop.future(appointmentSaved)
//        //                                    }
//                                    } else {
//                                        return req.eventLoop.future(appointmentSaved)
//                                    }
                                }
                            }
                        } else {
                            return appt.update(on: req.db).transform(to: appt).flatMap { appointmentSaved in

                                // Schedule SMS reminders for the appointment
                                return scheduleAppointmentReminders(req: req, appointment: appointmentSaved, member: member).flatMap { _ in
                                    return req.eventLoop.future(appointmentSaved)
                                }
                            }
                        }
                    }
                }
            }
        }
    }
    
    fileprivate func createZoomMeeting(req: Request, input: AppointmentInput) throws -> EventLoopFuture<Appointment> {
        //We may want to go the route of creating the zoom first.
        return try ZoomController().createMeeting(req: req, input: input.zoomMeeting()).flatMap { zoomResponse in
            
            //we must have zoom links to create an appointment. In the future we may want state for cases where we are not doing virutal appointments
            if let hostLink    = zoomResponse.start_url,
               let meetingLink = zoomResponse.join_url, let meetingID = zoomResponse.id {
                var updatedInput = input
                if var updatedMeta = input.meta {
                    updatedMeta.data["zoomMeetingID"] = "\(meetingID)"
                    updatedInput.meta = updatedMeta
                } else {
                    updatedInput.meta = MetaData(data: [ZoomMeetingMeta.zoomMeetingID.rawValue: "\(meetingID)"])
                }
                
                return try! createStandardAppointment(req: req, input: updatedInput, hostLink: hostLink, meetingLink: meetingLink)
                
            } else {
                return req.eventLoop.makeFailedFuture(NetworkError.error(type: .badRequest, msg: "Could not create zoom links. Please try again."))
            }
        }
    }
    
    static func find(req: Request, id: String) throws -> EventLoopFuture<Appointment> {
        return Appointment.query(on: req.db).filter(\.$id == UUID(uuidString: id)!).with(\.$member).with(\.$host).first().unwrap(or:  Abort(.notFound))
    }
    
    static func findTaskAppointment(req: Request, uuid: UUID?) throws -> EventLoopFuture<Appointment?> {
        return Appointment.query(on: req.db)
            .filter(\.$taskId == uuid)
            .with(\.$member)
            .with(\.$host)
            .first()
    }
                    
    
    func delete(req:Request) throws -> EventLoopFuture<Appointment> {
        guard let id = req.parameters.get("aptID") else { throw NetworkError.error(type: .appointments) }
        return try! AppointmentsController.find(req: req, id: id).flatMap { apt in

            // Cancel SMS reminders before deleting
            let cancelReminders = cancelAppointmentReminders(req: req, appointmentId: apt.id!)

            return cancelReminders.flatMap { _ in
                if apt.isVirtual(), let zoomMeetingID = apt.meta?.data[ZoomMeetingMeta.zoomMeetingID.rawValue] {
                    return try! ZoomController().deleteZoomMeeting(req: req, zoomMeetingID: zoomMeetingID).flatMap { response in
                        return apt.delete(on: req.db).transform(to: apt)
                    }
                } else {
                    return apt.delete(on: req.db).transform(to: apt)
                }
            }
        }
    }
    
    func cancelAppointmentTimelineItem(req: Request, appt:Appointment, navId: UUID) throws -> EventLoopFuture<Void> {
        guard let member = appt.member else { throw NetworkError.error(type: .appointments) }
        let message = TimeLineItemMessage.generalMemberUpdate(member: member,
                                                title: "Appointment Canceled",
                                                              desc: "\(appt.title) canceled.",
                                                status: "canceled",
                                                visible: true,
                                                              meta: .init(data: ["appt_id" : appt.id?.uuidString ?? ""]))
        return try TimelineControllerController.create([message.toTimelineItem()],
                                            creatorId: navId,
                                                       req: req)
    }

    // MARK: - SMS Reminder Scheduling

    private func scheduleAppointmentReminders(req: Request, appointment: Appointment, member: Member) -> EventLoopFuture<Void> {
        // Only schedule reminders if we have a valid appointment date and phone number
        guard let scheduleEpoc = appointment.scheduleEpoc,
              let appointmentId = appointment.id,
              let phoneNumber = member.smsPhone(),
              !phoneNumber.isEmpty else {
            req.logger.info("Skipping SMS reminder scheduling - missing required data")
            return req.eventLoop.makeSucceededFuture(())
        }

        let appointmentDate = Date(timeIntervalSince1970: TimeInterval(scheduleEpoc))
        let memberName = member.fullName()
        let appointmentTitle = appointment.title

        return req.smsReminderScheduler.scheduleAppointmentReminders(
            req: req,
            appointmentId: appointmentId,
            phoneNumber: phoneNumber,
            appointmentDate: appointmentDate,
            memberName: memberName,
            appointmentTitle: appointmentTitle
        ).flatMapError { error in
            req.logger.error("Failed to schedule SMS reminders for appointment \(appointmentId): \(error)")
            // Don't fail the appointment creation if reminder scheduling fails
            return req.eventLoop.makeSucceededFuture(())
        }
    }

    private func cancelAppointmentReminders(req: Request, appointmentId: UUID) -> EventLoopFuture<Void> {
        return req.smsReminderScheduler.cancelAppointmentReminders(req: req, appointmentId: appointmentId)
            .flatMapError { error in
                req.logger.error("Failed to cancel SMS reminders for appointment \(appointmentId): \(error)")
                // Don't fail the operation if reminder cancellation fails
                return req.eventLoop.makeSucceededFuture(())
            }
    }


//    //MARK: - Query
    fileprivate func buildQuery(query:QueryBuilder<Appointment>, req:Request) -> QueryBuilder<Appointment> {
        let status:String?             = req.query["status"]
        let kind:String?               = req.query["kind"]
        let creator:String?            = req.query["creatorID"]
        let member:String?             = req.query["memberID"]
        let startDateEpoch:Int?        = req.query["startDateEpoch"] //epoch
        
        if let creator = creator {
            query.filter(\.$creatorID == creator)
        }
        
        if let sts = status {
            query.filter(\.$status == sts)
        }
        
        if let kind = kind {
            query.filter(\.$kind == kind)
        }
        
        if let memberID = member {
            query.filter(\.$memberID == memberID.lowercased())
        }
        
        if let startDateEpoch {
            let todayEpoch = Date(timeIntervalSince1970: Double(startDateEpoch))
            print(todayEpoch)
            query.filter(\.$scheduleEpoc, .greaterThanOrEqual,  Int(todayEpoch.startOfDay.timeIntervalSince1970))
            query.filter(\.$scheduleEpoc, .lessThanOrEqual,  Int(todayEpoch.endOfDay.timeIntervalSince1970))
        }
        
        return query
            .with(\.$member)
            .with(\.$host, { user in
                user.with(\.$attachments)
            })
    }
}





struct ScheduleQuery: Content {
    var status: String?
    var kind: String?
    var creatorID: String?
    var memberID: String?
    var dateEpoch: Int?
    var orgId: String
}


struct ScheduleController {
    
    
    func getSchedule(req: Request) async throws -> ScheduleDataResponse {
        // Get the user ID from the request
        let queryParams = try req.query.decode(ScheduleQuery.self)
        
        let query = TaskModel.query(on: req.db)
        
        query.filter(\.$org.$id == UUID(uuidString: queryParams.orgId)!)
        
        
        // Fetch tasks for the user
        if let assignee = queryParams.creatorID {
            query.filter(\.$assignee.$id == UUID(uuidString: assignee))
        }
        
        if let memberID = queryParams.memberID {
            query
                .join(siblings: \.$receivers)
                .filter(Member.self, \.$id == UUID(uuidString: memberID)!)
        }
        
        if let dateEpoch = queryParams.dateEpoch {
            let date = Date(timeIntervalSince1970: TimeInterval(dateEpoch))
            var calendar = Calendar(identifier: .gregorian)
            calendar.timeZone = TimeZone(secondsFromGMT: 0)! // UTC

            let startOfDay = calendar.startOfDay(for: date)
            guard let endOfDay = calendar.date(byAdding: .day, value: 1, to: startOfDay) else {
                throw Abort(.internalServerError, reason: "Unable to calculate end of day")
            }

            let startEpoch = Int(startOfDay.timeIntervalSince1970)
            let endEpoch = Int(endOfDay.timeIntervalSince1970)

            query
                .filter(\.$dueAtEpoc >= startEpoch)
                .filter(\.$dueAtEpoc < endEpoch)
        }
        
        if let status = queryParams.status {
            query.filter(\.$status == status)
        }
        
        
        //Don't return archive
        query.filter(\.$status != "archive")
        
        let tasks = try await query
            .with(\.$receivers)
            .filter(\.$dueAtEpoc != nil)
            .sort(\.$dueAtEpoc)
            .all()
        
        // For this example, we'll assume you have a repository or service for appointments
        // Adjust as needed for your actual data fetching logic
        let results = try await getAppointmentsForUser(params: queryParams, req: req)
        
        return createScheduleResponse(tasks: tasks, appointments: results.appointment, member: results.member)
    }
    
    // In a real implementation, replace this with your actual appointment fetching logic
    private func getAppointmentsForUser(params: ScheduleQuery, req: Request) async throws -> (appointment:[Appointment], member: Member?) {
        // This is a placeholder - replace with your actual appointment fetching logic
        // Perhaps you have an AppointmentModel in your database
        let query = Appointment.query(on: req.db)
            .with(\.$member)
        
        query.filter(\.$org.$id == UUID(uuidString: params.orgId)!)
        
        if let creatorID = params.creatorID {
            query.filter(\.$creatorID == creatorID)
        }
        
        if let memberID = params.memberID {
            query.filter(\.$memberID == memberID)
        }
        
        if let kind = params.kind {
            query.filter(\.$kind == kind)
        }
        
        if let dateEpoch = params.dateEpoch {
            let date = Date(timeIntervalSince1970: TimeInterval(dateEpoch))
            var calendar = Calendar(identifier: .gregorian)
            calendar.timeZone = TimeZone(secondsFromGMT: 0)! // UTC

            let startOfDay = calendar.startOfDay(for: date)
            guard let endOfDay = calendar.date(byAdding: .day, value: 1, to: startOfDay) else {
                throw Abort(.internalServerError, reason: "Unable to calculate end of day")
            }

            let startEpoch = Int(startOfDay.timeIntervalSince1970)
            let endEpoch = Int(endOfDay.timeIntervalSince1970)

            query
                .filter(\.$scheduleEpoc >= startEpoch)
                .filter(\.$scheduleEpoc < endEpoch)
        }
        
        if let status = params.status {
            query.filter(\.$status == status)
        }
        
        let member = try await Member.query(on: req.db)
            .filter(\.$id == UUID(uuidString: params.memberID ?? "") ?? UUID())
            .first()
        
        //dont include task appointments cause its duplicated.
        query.filter(\.$taskId == nil)
        
        
        let apts = try await query.all()
        
        return(apts, member)
    }
    
    private func createScheduleResponse(tasks: [TaskModel], appointments: [Appointment], member: Member? = nil) -> ScheduleDataResponse {
        var activeGroups: [String: [ItemResponse]] = [:]
        var completedGroups: [String: [ItemResponse]] = [:]
        
        // Process tasks
        for task in tasks {
            let itemResponse = ItemResponse(from: task, title: task.title)
            let dateKey = task.dateString
            
            if task.isCompleted {
                completedGroups[dateKey, default: []].append(itemResponse)
            } else {
                activeGroups[dateKey, default: []].append(itemResponse)
            }
        }
        
        // Process appointments
        for appointment in appointments {
            let memberData = member != nil ? member : appointment.member
            let itemResponse = ItemResponse(from: appointment, title: appointment.title, member: memberData)
            let dateKey = appointment.dateString
            
            if appointment.isCompleted {
                completedGroups[dateKey, default: []].append(itemResponse)
            } else {
                activeGroups[dateKey, default: []].append(itemResponse)
            }
        }
        
        // Sort items within each date group by time
        for (date, items) in activeGroups {
            activeGroups[date] = items.sorted {
                getTime($0.time) < getTime($1.time)
            }
        }
        
        for (date, items) in completedGroups {
            completedGroups[date] = items.sorted {
                getTime($0.time) < getTime($1.time)
            }
        }
        
        // Convert to DateGroup arrays and sort by date
        let activeDateGroups = createSortedDateGroups(from: activeGroups)
        let completedDateGroups = createSortedDateGroups(from: completedGroups)
        return ScheduleDataResponse(active: activeDateGroups, completed: completedDateGroups)
    }
    
    private func createSortedDateGroups(from groups: [String: [ItemResponse]]) -> [DateGroup] {
        let dateFormatter = DateFormatter()
        dateFormatter.dateFormat = "EEE, MMM d, yyyy"
        
        return groups.map { DateGroup(date: $0.key, items: $0.value) }
            .sorted {
                guard let date1 = dateFormatter.date(from: $0.date),
                      let date2 = dateFormatter.date(from: $1.date) else {
                    return $0.date < $1.date
                }
                return date1 < date2
            }
    }
    
    private func getTime(_ timeString: String) -> Date {
        let timeFormatter = DateFormatter()
        timeFormatter.dateFormat = "h:mm a"
        return timeFormatter.date(from: timeString) ?? Date()
    }
}

// Extension to register routes
extension ScheduleController: RouteCollection {
    func boot(routes: RoutesBuilder) throws {
        let scheduleRoutes = routes.grouped("schedule")
        scheduleRoutes.get(use: getSchedule)        
    }
}

// Extension to register this route collection with your Vapor app
extension Application {
    func registerScheduleRoutes() {
        let scheduleController = ScheduleController()
        try! routes.register(collection: scheduleController)
    }
}
