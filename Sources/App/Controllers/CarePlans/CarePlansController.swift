//
//  CarePlansController.swift
//  hmbl-core
//
//  Created by <PERSON> on 5/28/25.
//
import Foundation
import Vapor
import Fluent
import JWTKit
import APNS
// routes.swift
struct CarePlansController: RouteCollection {
   
    
    // routes.swift
    func boot(routes: any Vapor.RoutesBuilder) throws {
        let carePlans = routes.grouped("api", "members", ":memberID", "careplans")
        carePlans.post(use: createCarePlan)
        carePlans.get(use: listCarePlans)

        let carePlan = routes.grouped("api", "careplans", ":carePlanID")
        carePlan.get(use: getCarePlan)
        carePlan.put(use: updateCarePlan)
        carePlan.delete(use: deleteCarePlan)

        // Goals
        let goals = carePlan.grouped("goals")
        goals.post(use: createGoal)
        goals.get(use: listGoals)
        goals.get(":goalID", use: getGoal)
        goals.put(":goalID", use: updateGoal)
        goals.delete(":goalID", use: deleteGoal)

        // Interventions
        let interventions = carePlan.grouped("interventions")
        interventions.post(use: createIntervention)
        interventions.get(use: listInterventions)
        interventions.get(":interventionID", use: getIntervention)
        interventions.put(":interventionID", use: updateIntervention)
        interventions.delete(":interventionID", use: deleteIntervention)

        // Problems
        let problems = carePlan.grouped("problems")
        problems.post(use: createProblem)
        problems.get(use: listProblems)
        problems.get(":problemID", use: getProblem)
        problems.put(":problemID", use: updateProblem)
        problems.delete(":problemID", use: deleteProblem)

        // Care Team Members
        let teamMembers = carePlan.grouped("team-members")
        teamMembers.post(use: createCareTeamMember)
        teamMembers.post("from-team", use: addTeamMembersToCarePlan)
        teamMembers.get(use: listCareTeamMembers)
        teamMembers.put(":memberID", use: updateCareTeamMember)
        teamMembers.delete(":memberID", use: deleteCareTeamMember)
        teamMembers.delete("bulk", use: bulkDeleteCareTeamMembers)

        // Reviews
        let reviews = carePlan.grouped("reviews")
        reviews.post(use: createCarePlanReview)
        reviews.get(use: listCarePlanReviews)
        reviews.get(":reviewID", use: getCarePlanReview)
        reviews.put(":reviewID", use: updateCarePlanReview)
        reviews.delete(":reviewID", use: deleteCarePlanReview)

        // Follow-ups
        let followUps = carePlan.grouped("followups")
        followUps.post(use: createFollowUp)
        followUps.get(use: listFollowUps)
        followUps.get(":followUpID", use: getFollowUp)
        followUps.put(":followUpID", use: updateFollowUp)
        followUps.delete(":followUpID", use: deleteFollowUp)

        // Services
        let services = carePlan.grouped("services")
        services.post(use: createCarePlanService)
        services.get(use: listCarePlanServices)
        services.get(":serviceID", use: getCarePlanService)
        services.put(":serviceID", use: updateCarePlanService)
        services.delete(":serviceID", use: deleteCarePlanService)

        // Notes
        let notes = carePlan.grouped("notes")
        notes.post(use: createNote)
        notes.get(use: listNotes)
        notes.get(":noteID", use: getNote)
        notes.put(":noteID", use: updateNote)
        notes.delete(":noteID", use: deleteNote)

        // Timeline Items
        let timelineItems = carePlan.grouped("timeline-items")
        timelineItems.post(use: createTimelineItem)
        timelineItems.get(use: listTimelineItems)
        timelineItems.get(":timelineItemID", use: getTimelineItem)
        timelineItems.put(":timelineItemID", use: updateTimelineItem)
        timelineItems.delete(":timelineItemID", use: deleteTimelineItem)
    }
    
    
    // Controller stubs for CarePlanService
    func createCarePlanService(req: Request) async throws -> CarePlanService {
        let input = try req.content.decode(CarePlanServiceCreateRequest.self)
        let carePlanID = try req.parameters.require("carePlanID", as: UUID.self)
        let user = try await TokenController.userIdFromToken(req: req)

        // Validate that the CarePlan exists and get member information
        guard let carePlan = try await CarePlan.query(on: req.db)
            .filter(\.$id == carePlanID)
            .with(\.$member)
            .first() else {
            throw Abort(.notFound, reason: "CarePlan with ID \(carePlanID) not found")
        }

        let service = CarePlanService()
        service.$carePlan.id = carePlanID
        service.cboName = input.cboName
        service.staffName = input.staffName
        service.addedBy = input.addedBy
        service.status = input.status
        service.appointmentDate = input.appointmentDate
        service.outcomeReasonType = input.outcomeReasonType
        service.outcomeReasonDescription = input.outcomeReasonDescription
        service.refId = input.refId

        try await service.save(on: req.db)

        // Create timeline entry for CarePlanService creation
        try await CarePlanTimelineService.createCarePlanServiceTimeline(
            operation: .created,
            service: service,
            on: req.db,
            creatorID: user,
            memberID: carePlan.$member.id
        )

        return service
    }

    func listCarePlanServices(req: Request) async throws -> [CarePlanService] {
        let carePlanID = try req.parameters.require("carePlanID", as: UUID.self)
        return try await CarePlanService.query(on: req.db)
            .filter(\.$carePlan.$id == carePlanID).all()
    }

    func getCarePlanService(req: Request) async throws -> CarePlanService {
        let id = try req.parameters.require("serviceID", as: UUID.self)
        guard let service = try await CarePlanService.query(on: req.db)
            .filter(\.$id == id)
            .with(\.$carePlan, { carePlan in
                carePlan.with(\.$member)
            })
            .first() else {
            throw Abort(.notFound, reason: "CarePlanService with ID \(id) not found")
        }
        return service
    }

    func updateCarePlanService(req: Request) async throws -> CarePlanService {
        let id = try req.parameters.require("serviceID", as: UUID.self)
        let input = try req.content.decode(CarePlanServiceCreateRequest.self)
        let user = try await TokenController.userIdFromToken(req: req)
        guard let service = try await CarePlanService.find(id, on: req.db) else {
            throw Abort(.notFound)
        }

        // Get the care plan with member information to access memberID
        guard let carePlan = try await CarePlan.query(on: req.db)
            .filter(\.$id == service.$carePlan.id)
            .with(\.$member)
            .first() else {
            throw Abort(.notFound, reason: "CarePlan not found for service")
        }

        service.cboName = input.cboName
        service.staffName = input.staffName
        service.addedBy = input.addedBy
        service.status = input.status
        service.appointmentDate = input.appointmentDate
        service.outcomeReasonType = input.outcomeReasonType
        service.outcomeReasonDescription = input.outcomeReasonDescription
        service.refId = input.refId
        try await service.update(on: req.db)

        // Create timeline entry for CarePlanService update
        try await CarePlanTimelineService.createCarePlanServiceTimeline(
            operation: .updated,
            service: service,
            on: req.db,
            creatorID: user,
            memberID: carePlan.$member.id
        )

        return service
    }

    func deleteCarePlanService(req: Request) async throws -> HTTPStatus {
        let id = try req.parameters.require("serviceID", as: UUID.self)
        let user = try await TokenController.userIdFromToken(req: req)
        guard let service = try await CarePlanService.find(id, on: req.db) else {
            throw Abort(.notFound)
        }

        // Get the care plan with member information to access memberID
        guard let carePlan = try await CarePlan.query(on: req.db)
            .filter(\.$id == service.$carePlan.id)
            .with(\.$member)
            .first() else {
            throw Abort(.notFound, reason: "CarePlan not found for service")
        }

        // Create timeline entry for CarePlanService deletion before deleting
        try await CarePlanTimelineService.createCarePlanServiceTimeline(
            operation: .deleted,
            service: service,
            on: req.db,
            creatorID: user,
            memberID: carePlan.$member.id
        )

        try await service.delete(on: req.db)
        return .noContent
    }
    
    // Controller stubs for CarePlanFollowUp
    func createFollowUp(req: Request) async throws -> CarePlanFollowUp {
        let input = try req.content.decode(CarePlanFollowUpCreateRequest.self)
        let carePlanID = try req.parameters.require("carePlanID", as: UUID.self)
        let user = try await TokenController.userIdFromToken(req: req)

        // Validate that the CarePlan exists and get member information
        guard let carePlan = try await CarePlan.query(on: req.db)
            .filter(\.$id == carePlanID)
            .with(\.$member)
            .first() else {
            throw Abort(.notFound, reason: "CarePlan with ID \(carePlanID) not found")
        }

        let followUp = CarePlanFollowUp()
        followUp.$carePlan.id = carePlanID
        followUp.datetime = input.datetime
        followUp.type = input.type
        followUp.outcome = input.outcome ?? ""
        followUp.notes = input.notes
        followUp.staffName = input.staffName
        followUp.staffRole = input.staffRole

        try await followUp.save(on: req.db)

        // Schedule SMS reminder for follow-up appointment
        try await scheduleFollowUpReminders(req: req, followUp: followUp, member: carePlan.member)

        // Update CarePlan's lastReviewed with the follow-up datetime
        carePlan.lastReviewed = followUp.datetime
        try await carePlan.update(on: req.db)

        // Create timeline entry for CarePlanFollowUp creation
        try await CarePlanTimelineService.createCarePlanFollowUpTimeline(
            operation: .created,
            followUp: followUp,
            on: req.db,
            creatorID: user,
            memberID: carePlan.$member.id
        )

        return followUp
    }

    func listFollowUps(req: Request) async throws -> [CarePlanFollowUp] {
        let carePlanID = try req.parameters.require("carePlanID", as: UUID.self)
        return try await CarePlanFollowUp.query(on: req.db).filter(\.$carePlan.$id == carePlanID).all()
    }

    func getFollowUp(req: Request) async throws -> CarePlanFollowUp {
        let followUpID = try req.parameters.require("followUpID", as: UUID.self)
        guard let review = try await CarePlanFollowUp.query(on: req.db)
            .filter(\.$id == followUpID)
            .with(\.$carePlan, { carePlan in
                carePlan.with(\.$member)
            })
            .first() else {
            throw Abort(.notFound, reason: "CarePlanFollowUp with ID \(followUpID) not found")
        }
        return review
    }

    func updateFollowUp(req: Request) async throws -> CarePlanFollowUp {
        let followUpID = try req.parameters.require("followUpID", as: UUID.self)
        let input = try req.content.decode(CarePlanFollowUpCreateRequest.self)
        let user = try await TokenController.userIdFromToken(req: req)
        guard let followUp = try await CarePlanFollowUp.find(followUpID, on: req.db) else {
            throw Abort(.notFound)
        }

        // Get the care plan with member information to access memberID
        guard let carePlan = try await CarePlan.query(on: req.db)
            .filter(\.$id == followUp.$carePlan.id)
            .with(\.$member)
            .first() else {
            throw Abort(.notFound, reason: "CarePlan not found for follow-up")
        }

        followUp.datetime = input.datetime
        followUp.type = input.type
        followUp.outcome = input.outcome ?? ""
        followUp.notes = input.notes
        followUp.staffName = input.staffName
        followUp.staffRole = input.staffRole
        try await followUp.update(on: req.db)

        // Handle follow-up reminder scheduling/cancellation
        try await handleFollowUpReminderUpdate(req: req, followUp: followUp, member: carePlan.member)

        // Update CarePlan's lastReviewed with the follow-up datetime
        carePlan.lastReviewed = followUp.datetime
        try await carePlan.update(on: req.db)

        // Create timeline entry for CarePlanFollowUp update
        try await CarePlanTimelineService.createCarePlanFollowUpTimeline(
            operation: .updated,
            followUp: followUp,
            on: req.db,
            creatorID: user,
            memberID: carePlan.$member.id
        )

        return followUp
    }

    func deleteFollowUp(req: Request) async throws -> HTTPStatus {
        let followUpID = try req.parameters.require("followUpID", as: UUID.self)
        let user = try await TokenController.userIdFromToken(req: req)
        guard let followUp = try await CarePlanFollowUp.find(followUpID, on: req.db) else {
            throw Abort(.notFound)
        }

        // Get the care plan with member information to access memberID
        guard let carePlan = try await CarePlan.query(on: req.db)
            .filter(\.$id == followUp.$carePlan.id)
            .with(\.$member)
            .first() else {
            throw Abort(.notFound, reason: "CarePlan not found for follow-up")
        }

        // Create timeline entry for CarePlanFollowUp deletion before deleting
        try await CarePlanTimelineService.createCarePlanFollowUpTimeline(
            operation: .deleted,
            followUp: followUp,
            on: req.db,
            creatorID: user,
            memberID: carePlan.$member.id
        )

        try await followUp.delete(on: req.db)
        return .noContent
    }
    
    // Controller stubs for CarePlanReview
    func createCarePlanReview(req: Request) async throws -> CarePlanReview {
        let input = try req.content.decode(CarePlanReviewCreateRequest.self)
        let carePlanID = try req.parameters.require("carePlanID", as: UUID.self)
        let user = try await TokenController.userIdFromToken(req: req)

        // Validate that the CarePlan exists and get member information
        guard let carePlan = try await CarePlan.query(on: req.db)
            .filter(\.$id == carePlanID)
            .with(\.$member)
            .first() else {
            throw Abort(.notFound, reason: "CarePlan with ID \(carePlanID) not found")
        }

        let review = CarePlanReview()
        review.$carePlan.id = carePlanID
        review.title = input.title
        review.reviewDate = input.reviewDate
        review.notes = input.notes
        review.reviewerName = input.reviewerName
        review.reviewerRole = input.reviewerRole

        try await review.save(on: req.db)

        // Create timeline entry for CarePlanReview creation
        try await CarePlanTimelineService.createCarePlanReviewTimeline(
            operation: .created,
            review: review,
            on: req.db,
            creatorID: user,
            memberID: carePlan.$member.id
        )

        return review
    }

    func listCarePlanReviews(req: Request) async throws -> [CarePlanReview] {
        let carePlanID = try req.parameters.require("carePlanID", as: UUID.self)
        return try await CarePlanReview.query(on: req.db).filter(\.$carePlan.$id == carePlanID).all()
    }

    func getCarePlanReview(req: Request) async throws -> CarePlanReview {
        let reviewID = try req.parameters.require("reviewID", as: UUID.self)
        guard let review = try await CarePlanReview.query(on: req.db)
            .filter(\.$id == reviewID)
            .with(\.$carePlan, { carePlan in
                carePlan.with(\.$member)
            })
            .first() else {
            throw Abort(.notFound, reason: "CarePlanReview with ID \(reviewID) not found")
        }
        return review
    }
    
    // Controller stubs for CareTeamMember
    func createCareTeamMember(req: Request) async throws -> CareTeamMember {
        let input = try req.content.decode(CareTeamMemberCreateRequest.self)
        let carePlanID = try req.parameters.require("carePlanID", as: UUID.self)
        let user = try await TokenController.userIdFromToken(req: req)

        // Validate that the CarePlan exists and get member information
        guard let carePlan = try await CarePlan.query(on: req.db)
            .filter(\.$id == carePlanID)
            .with(\.$member)
            .first() else {
            throw Abort(.notFound, reason: "CarePlan with ID \(carePlanID) not found")
        }

        let member = CareTeamMember()
        member.$carePlan.id = carePlanID
        member.userID = input.userID
        member.name = input.name
        member.role = input.role
        member.contactInfo = input.contactInfo

        try await member.save(on: req.db)

        // Create timeline entry for CareTeamMember creation
        try await CarePlanTimelineService.createCareTeamMemberTimeline(
            operation: .created,
            member: member,
            on: req.db,
            creatorID: user,
            memberID: carePlan.$member.id
        )

        return member
    }

    func listCareTeamMembers(req: Request) async throws -> [CareTeamMember] {
        let carePlanID = try req.parameters.require("carePlanID", as: UUID.self)
        return try await CareTeamMember.query(on: req.db).filter(\.$carePlan.$id == carePlanID).all()
    }

    func addTeamMembersToCarePlan(req: Request) async throws -> [CareTeamMember] {
        let input = try req.content.decode(AddTeamMembersRequest.self)
        let carePlanID = try req.parameters.require("carePlanID", as: UUID.self)
        let user = try await TokenController.userIdFromToken(req: req)

        // Validate that the CarePlan exists and get member information
        guard let carePlan = try await CarePlan.query(on: req.db)
            .filter(\.$id == carePlanID)
            .with(\.$member)
            .first() else {
            throw Abort(.notFound, reason: "CarePlan with ID \(carePlanID) not found")
        }

        // Fetch the team with its navigators
        guard let team = try await Team.query(on: req.db)
            .filter(\.$id == input.teamID)
            .with(\.$navigators)
            .first() else {
            throw Abort(.notFound, reason: "Team with ID \(input.teamID) not found")
        }

        var createdMembers: [CareTeamMember] = []

        // Create CareTeamMember for each navigator in the team
        for navigator in team.navigators {
            let member = CareTeamMember()
            member.$carePlan.id = carePlanID
            member.userID = navigator.id
            member.name = "\(navigator.firstName) \(navigator.lastName)"
            member.role = "Navigator"
            member.contactInfo = navigator.email

            try await member.save(on: req.db)

            // Create timeline entry for CareTeamMember creation
            try await CarePlanTimelineService.createCareTeamMemberTimeline(
                operation: .created,
                member: member,
                on: req.db,
                creatorID: user,
                memberID: carePlan.$member.id
            )

            createdMembers.append(member)
        }

        return createdMembers
    }
    
    // Controller stubs for Problem
    func createProblem(req: Request) async throws -> Problem {
        let input = try req.content.decode(ProblemCreateRequest.self)
        let carePlanID = try req.parameters.require("carePlanID", as: UUID.self)
        let user = try await TokenController.userIdFromToken(req: req)

        // Validate that the CarePlan exists and get member information
        guard let carePlan = try await CarePlan.query(on: req.db)
            .filter(\.$id == carePlanID)
            .with(\.$member)
            .first() else {
            throw Abort(.notFound, reason: "CarePlan with ID \(carePlanID) not found")
        }

        let problem = Problem()
        problem.$carePlan.id = carePlanID
        problem.title = input.title
        problem.icdCode = input.icdCode
        problem.description = input.description
        problem.clinicalNote = input.clinicalNote
        problem.status = input.status
        problem.dateIdentified = input.dateIdentified
        problem.source = input.source
        problem.confirmedBy = input.confirmedBy

        try await problem.save(on: req.db)

        // Create timeline entry for Problem creation
        try await CarePlanTimelineService.createProblemTimeline(
            operation: .created,
            problem: problem,
            on: req.db,
            creatorID: user,
            memberID: carePlan.$member.id
        )

        return problem
    }

    func listProblems(req: Request) async throws -> [Problem] {
        let carePlanID = try req.parameters.require("carePlanID", as: UUID.self)
        return try await Problem.query(on: req.db).filter(\.$carePlan.$id == carePlanID).all()
    }

    func getProblem(req: Request) async throws -> Problem {
        let problemID = try req.parameters.require("problemID", as: UUID.self)
        guard let problem = try await Problem.query(on: req.db)
            .filter(\.$id == problemID)
            .with(\.$carePlan, { carePlan in
                carePlan.with(\.$member)
            })
            .with(\.$members)
            .first() else {
            throw Abort(.notFound, reason: "Problem with ID \(problemID) not found")
        }
        return problem
    }
    
    // Controller stubs for Intervention
    func createIntervention(req: Request) async throws -> Intervention {
        let input = try req.content.decode(InterventionCreateRequest.self)
        let carePlanID = try req.parameters.require("carePlanID", as: UUID.self)
        let user = try await TokenController.userFromToken(req: req)

        
        // Get the care plan with member information
        guard let carePlan = try await CarePlan.query(on: req.db)
            .filter(\.$id == carePlanID)
            .with(\.$member)
            .first() else {
            throw Abort(.notFound, reason: "CarePlan with ID \(carePlanID) not found")
        }
        
        guard let userId =  user.id  else {
            throw Abort(.notFound, reason: "No User Found")
        }
        
        guard let orgId =  user.org?.id  else {
            throw Abort(.notFound, reason: "No Org Found")
        }

        let intervention = Intervention()
        intervention.$carePlan.id = carePlanID
        intervention.title = input.title
        intervention.action = input.action
        intervention.responsibleParty = input.responsibleParty
        intervention.responsiblePartyId = input.responsiblePartyId
        intervention.status = input.status
        intervention.dueDate = input.dueDate
        intervention.note = input.note

        try await intervention.save(on: req.db)

        // Schedule SMS reminder for intervention due date
        try await scheduleInterventionReminders(req: req, intervention: intervention, member: carePlan.member)

        // Create timeline entry for Intervention creation
        try await CarePlanTimelineService.createInterventionTimeline(
            operation: .created,
            intervention: intervention,
            on: req.db,
            creatorID: userId,
            memberID: carePlan.$member.id
        )

        // Auto-assign task if requested
        if input.shouldAutoAssignTask {
            do {
                try await createTaskFromIntervention(
                    intervention: intervention,
                    carePlanID: carePlanID,
                    creatorID: userId,
                    orgId: orgId,
                    on: req
                )
            } catch {
                // Log error but don't fail intervention creation
                req.logger.error("Failed to create auto-assigned task for intervention \(intervention.id?.uuidString ?? "unknown"): \(error)")
            }
        }

        return intervention
    }

    // MARK: - Helper function to create task from intervention
    private func createTaskFromIntervention(
        intervention: Intervention,
        carePlanID: UUID,
        creatorID: UUID,
        orgId: UUID,
        on req: Request
    ) async throws {
        // Only create task if responsiblePartyId is provided
        guard let responsiblePartyId = intervention.responsiblePartyId else {
            req.logger.info("Skipping task creation: no responsiblePartyId provided for intervention \(intervention.id?.uuidString ?? "unknown")")
            return
        }

        // Get the care plan with member information
        guard let carePlan = try await CarePlan.query(on: req.db)
            .filter(\.$id == carePlanID)
            .with(\.$member)
            .first() else {
            throw Abort(.notFound, reason: "CarePlan not found")
        }

        // Convert Date to Unix timestamp
        let dueAtEpoch = Int(intervention.dueDate.timeIntervalSince1970)

        // Create TasksCreateInput
        let taskInput = TasksCreateInput(
            title: intervention.title ?? intervention.action,
            type: "intervention",
            status: "pending",
            remote: false,
            dueAtEpoc: dueAtEpoch,
            desc: intervention.action,
            meta: nil,
            orgID: orgId.uuidString,
            creatorID: creatorID.uuidString,
            assigneeID: responsiblePartyId,
            location: nil,
            taskDetail: TaskDetailInput(
                title: intervention.title ?? intervention.action,
                type: "intervention",
                kind: "care_plan",
                meta: MetaData(data: ["care_plan_id": carePlanID.uuidString])
            ),
            completedBy: [responsiblePartyId],
            receivers: [carePlan.member.id?.uuidString].compactMap { $0 },
            urgent: false,
            meetinglink: nil,
            attachments: nil
        )

        // Create the task using TasksController
        let tasksController = TasksController()

        // Create a new request with the task input
        let taskRequest = req
        try taskRequest.content.encode(taskInput)

        let _ = try await tasksController.create(req: taskRequest).get()

        req.logger.info("Successfully created auto-assigned task for intervention \(intervention.id?.uuidString ?? "unknown")")
    }

    func listInterventions(req: Request) async throws -> [Intervention] {
        let carePlanID = try req.parameters.require("carePlanID", as: UUID.self)
        return try await Intervention.query(on: req.db).filter(\.$carePlan.$id == carePlanID).all()
    }

    func getIntervention(req: Request) async throws -> Intervention {
        let interventionID = try req.parameters.require("interventionID", as: UUID.self)
        guard let intervention = try await Intervention.query(on: req.db)
            .filter(\.$id == interventionID)
            .with(\.$carePlan, { carePlan in
                carePlan.with(\.$member)
            })
            .first() else {
            throw Abort(.notFound, reason: "Intervention with ID \(interventionID) not found")
        }
        return intervention
    }
    
    // Controller stubs for Goal
    func createGoal(req: Request) async throws -> Goal {
        let input = try req.content.decode(GoalCreateRequest.self)
        let carePlanID = try req.parameters.require("carePlanID", as: UUID.self)
        let user = try await TokenController.userIdFromToken(req: req)

        // Validate that the CarePlan exists and get member information
        guard let carePlan = try await CarePlan.query(on: req.db)
            .filter(\.$id == carePlanID)
            .with(\.$member)
            .first() else {
            throw Abort(.notFound, reason: "CarePlan with ID \(carePlanID) not found")
        }

        let goal = Goal()
        goal.$carePlan.id = carePlanID
        goal.title = input.title
        goal.description = input.description
        goal.type = input.type
        goal.targetDate = input.targetDate
        goal.status = input.status
        goal.outcome = input.outcome
        goal.objective = input.objective
        goal.measurementCriteria = input.measurementCriteria
        goal.achievabilityNote = input.achievabilityNote
        goal.barriers = input.barriers

        try await goal.save(on: req.db)

        // Schedule SMS reminder for goal target date
        try await scheduleGoalReminders(req: req, goal: goal, member: carePlan.member)

        // Create timeline entry for Goal creation
        try await CarePlanTimelineService.createGoalTimeline(
            operation: .created,
            goal: goal,
            on: req.db,
            creatorID: user,
            memberID: carePlan.$member.id
        )

        return goal
    }

    func listGoals(req: Request) async throws -> [Goal] {
        let carePlanID = try req.parameters.require("carePlanID", as: UUID.self)
        return try await Goal.query(on: req.db).filter(\.$carePlan.$id == carePlanID).all()
    }

    func getGoal(req: Request) async throws -> Goal {
        let goalID = try req.parameters.require("goalID", as: UUID.self)
        guard let goal = try await Goal.query(on: req.db)
            .filter(\.$id == goalID)
            .with(\.$carePlan, { carePlan in
                carePlan.with(\.$member)
            })
            .first() else {
            throw Abort(.notFound, reason: "Goal with ID \(goalID) not found")
        }
        return goal
    }
    
    // Primary Care Plan Controller Stubs
    func createCarePlan(req: Request) async throws -> CarePlan {
        let input = try req.content.decode(CarePlanCreateRequest.self)
        let memberID = try req.parameters.require("memberID", as: UUID.self)
        let user = try await TokenController.userIdFromToken(req: req)

        let carePlan = CarePlan()
        carePlan.$member.id = memberID
        carePlan.title = input.title
        carePlan.startDate = input.startDate
        carePlan.lastReviewed = input.lastReviewed
        carePlan.nextReviewDate = input.nextReviewDate
        carePlan.outcome = input.outcome
        carePlan.status = input.status

        try await carePlan.save(on: req.db)

        // Create timeline entry for CarePlan creation
        try await CarePlanTimelineService.createCarePlanTimeline(
            operation: .created,
            carePlan: carePlan,
            on: req.db,
            creatorID: user,
            memberID: memberID
        )

        return carePlan
    }

    func listCarePlans(req: Request) async throws -> CarePlansListResponse {
        let memberID = try req.parameters.require("memberID", as: UUID.self)

        var query = CarePlan.query(on: req.db).filter(\.$member.$id == memberID)

        // Add status filtering if provided
        if let status = try? req.query.get(String.self, at: "status") {
            query = query.filter(\.$status == status)
        }

        // Get all care plans for the member
        let carePlans = try await query.sort(\.$createdAt, .descending).all()

        // If no care plans, return empty response
        guard !carePlans.isEmpty else {
            return CarePlansListResponse(carePlans: [])
        }

        // Extract care plan IDs for batch queries
        let carePlanIDs = carePlans.compactMap { $0.id }

        // Perform concurrent count queries for all care plans
        async let goalsCountsQuery = Goal.query(on: req.db)
            .filter(\.$carePlan.$id ~~ carePlanIDs)
            .field(\.$carePlan.$id)
            .all()

        async let interventionsCountsQuery = Intervention.query(on: req.db)
            .filter(\.$carePlan.$id ~~ carePlanIDs)
            .field(\.$carePlan.$id)
            .all()

        // Await results concurrently
        let goals = try await goalsCountsQuery
        let interventions = try await interventionsCountsQuery

        // Create count dictionaries for efficient lookup
        let goalsCountDict = Dictionary(grouping: goals, by: { $0.$carePlan.id })
            .mapValues { $0.count }
        let interventionsCountDict = Dictionary(grouping: interventions, by: { $0.$carePlan.id })
            .mapValues { $0.count }

        // Build response with counts
        let carePlansWithCounts = carePlans.map { carePlan in
            let carePlanID = carePlan.id
            return CarePlanWithCountsResponse(
                carePlan: carePlan,
                goalsCount: carePlanID != nil ? (goalsCountDict[carePlanID!] ?? 0) : 0,
                interventionsCount: carePlanID != nil ? (interventionsCountDict[carePlanID!] ?? 0) : 0
            )
        }

        return CarePlansListResponse(carePlans: carePlansWithCounts)
    }

    func getCarePlan(req: Request) async throws -> CarePlan {
        let id = try req.parameters.require("carePlanID", as: UUID.self)
        guard let plan = try await CarePlan.find(id, on: req.db) else {
            throw Abort(.notFound)
        }
        return plan
    }

    func updateCarePlan(req: Request) async throws -> CarePlan {
        let id = try req.parameters.require("carePlanID", as: UUID.self)
        let input = try req.content.decode(CarePlanCreateRequest.self)
        let user = try await TokenController.userIdFromToken(req: req)
        guard let plan = try await CarePlan.query(on: req.db)
            .filter(\.$id == id)
            .with(\.$member)
            .first() else {
            throw Abort(.notFound)
        }
        plan.title = input.title
        plan.startDate = input.startDate
        plan.lastReviewed = input.lastReviewed
        plan.nextReviewDate = input.nextReviewDate
        plan.outcome = input.outcome
        plan.status = input.status
        try await plan.update(on: req.db)

        // Create timeline entry for CarePlan update
        try await CarePlanTimelineService.createCarePlanTimeline(
            operation: .updated,
            carePlan: plan,
            on: req.db,
            creatorID: user,
            memberID: plan.$member.id
        )

        return plan
    }

    func deleteCarePlan(req: Request) async throws -> HTTPStatus {
        let id = try req.parameters.require("carePlanID", as: UUID.self)
        let user = try await TokenController.userIdFromToken(req: req)
        guard let plan = try await CarePlan.query(on: req.db)
            .filter(\.$id == id)
            .with(\.$member)
            .first() else {
            throw Abort(.notFound)
        }

        // Create timeline entry for CarePlan deletion before deleting
        try await CarePlanTimelineService.createCarePlanTimeline(
            operation: .deleted,
            carePlan: plan,
            on: req.db,
            creatorID: user,
            memberID: plan.$member.id
        )

        try await plan.delete(on: req.db)
        return .noContent
    }

    // Controller extensions
    func updateGoal(req: Request) async throws -> Goal {
        let id = try req.parameters.require("goalID", as: UUID.self)
        let input = try req.content.decode(GoalCreateRequest.self)
        let user = try await TokenController.userIdFromToken(req: req)
        guard let goal = try await Goal.find(id, on: req.db) else {
            throw Abort(.notFound)
        }

        // Get the care plan with member information to access memberID
        guard let carePlan = try await CarePlan.query(on: req.db)
            .filter(\.$id == goal.$carePlan.id)
            .with(\.$member)
            .first() else {
            throw Abort(.notFound, reason: "CarePlan not found for goal")
        }

        goal.title = input.title
        goal.description = input.description
        goal.type = input.type
        goal.targetDate = input.targetDate
        goal.status = input.status
        goal.outcome = input.outcome
        goal.objective = input.objective
        goal.measurementCriteria = input.measurementCriteria
        goal.achievabilityNote = input.achievabilityNote
        goal.barriers = input.barriers
        try await goal.update(on: req.db)

        // Handle goal reminder scheduling/cancellation
        try await handleGoalReminderUpdate(req: req, goal: goal, member: carePlan.member)

        // Create timeline entry for Goal update
        try await CarePlanTimelineService.createGoalTimeline(
            operation: .updated,
            goal: goal,
            on: req.db,
            creatorID: user,
            memberID: carePlan.$member.id
        )

        return goal
    }

    func deleteGoal(req: Request) async throws -> HTTPStatus {
        let id = try req.parameters.require("goalID", as: UUID.self)
        let user = try await TokenController.userIdFromToken(req: req)
        guard let goal = try await Goal.find(id, on: req.db) else {
            throw Abort(.notFound)
        }

        // Get the care plan with member information to access memberID
        guard let carePlan = try await CarePlan.query(on: req.db)
            .filter(\.$id == goal.$carePlan.id)
            .with(\.$member)
            .first() else {
            throw Abort(.notFound, reason: "CarePlan not found for goal")
        }

        // Cancel goal reminders before deleting
        try await cancelGoalReminders(req: req, goalId: goal.id!)

        // Create timeline entry for Goal deletion before deleting
        try await CarePlanTimelineService.createGoalTimeline(
            operation: .deleted,
            goal: goal,
            on: req.db,
            creatorID: user,
            memberID: carePlan.$member.id
        )

        try await goal.delete(on: req.db)
        return .noContent
    }

    func updateIntervention(req: Request) async throws -> Intervention {
        let id = try req.parameters.require("interventionID", as: UUID.self)
        let input = try req.content.decode(InterventionCreateRequest.self)
        let user = try await TokenController.userIdFromToken(req: req)
        guard let intervention = try await Intervention.find(id, on: req.db) else {
            throw Abort(.notFound)
        }

        // Get the care plan with member information to access memberID
        guard let carePlan = try await CarePlan.query(on: req.db)
            .filter(\.$id == intervention.$carePlan.id)
            .with(\.$member)
            .first() else {
            throw Abort(.notFound, reason: "CarePlan not found for intervention")
        }

        intervention.title = input.title
        intervention.action = input.action
        intervention.responsibleParty = input.responsibleParty
        intervention.responsiblePartyId = input.responsiblePartyId
        intervention.status = input.status
        intervention.dueDate = input.dueDate
        intervention.note = input.note
        try await intervention.update(on: req.db)

        // Handle intervention reminder scheduling/cancellation
        try await handleInterventionReminderUpdate(req: req, intervention: intervention, member: carePlan.member)

        // Create timeline entry for Intervention update
        try await CarePlanTimelineService.createInterventionTimeline(
            operation: .updated,
            intervention: intervention,
            on: req.db,
            creatorID: user,
            memberID: carePlan.$member.id
        )

        return intervention
    }

    func deleteIntervention(req: Request) async throws -> HTTPStatus {
        let id = try req.parameters.require("interventionID", as: UUID.self)
        let user = try await TokenController.userIdFromToken(req: req)
        guard let intervention = try await Intervention.find(id, on: req.db) else {
            throw Abort(.notFound)
        }

        // Get the care plan with member information to access memberID
        guard let carePlan = try await CarePlan.query(on: req.db)
            .filter(\.$id == intervention.$carePlan.id)
            .with(\.$member)
            .first() else {
            throw Abort(.notFound, reason: "CarePlan not found for intervention")
        }

        // Cancel intervention reminders before deleting
        try await cancelInterventionReminders(req: req, interventionId: intervention.id!)

        // Create timeline entry for Intervention deletion before deleting
        try await CarePlanTimelineService.createInterventionTimeline(
            operation: .deleted,
            intervention: intervention,
            on: req.db,
            creatorID: user,
            memberID: carePlan.$member.id
        )

        try await intervention.delete(on: req.db)
        return .noContent
    }

    func updateProblem(req: Request) async throws -> Problem {
        let id = try req.parameters.require("problemID", as: UUID.self)
        let input = try req.content.decode(ProblemCreateRequest.self)
        let user = try await TokenController.userIdFromToken(req: req)
        guard let problem = try await Problem.find(id, on: req.db) else {
            throw Abort(.notFound)
        }

        // Get the care plan with member information to access memberID
        guard let carePlan = try await CarePlan.query(on: req.db)
            .filter(\.$id == problem.$carePlan.id!)
            .with(\.$member)
            .first() else {
            throw Abort(.notFound, reason: "CarePlan not found for problem")
        }

        problem.title = input.title
        problem.icdCode = input.icdCode
        problem.description = input.description
        problem.clinicalNote = input.clinicalNote
        problem.status = input.status
        problem.dateIdentified = input.dateIdentified
        problem.source = input.source
        problem.confirmedBy = input.confirmedBy
        try await problem.update(on: req.db)

        // Create timeline entry for Problem update
        try await CarePlanTimelineService.createProblemTimeline(
            operation: .updated,
            problem: problem,
            on: req.db,
            creatorID: user,
            memberID: carePlan.$member.id
        )

        return problem
    }

    func deleteProblem(req: Request) async throws -> HTTPStatus {
        let id = try req.parameters.require("problemID", as: UUID.self)
        let user = try await TokenController.userIdFromToken(req: req)
        guard let problem = try await Problem.find(id, on: req.db) else {
            throw Abort(.notFound)
        }

        // Get the care plan with member information to access memberID
        guard let carePlan = try await CarePlan.query(on: req.db)
            .filter(\.$id == problem.$carePlan.id!)
            .with(\.$member)
            .first() else {
            throw Abort(.notFound, reason: "CarePlan not found for problem")
        }

        // Create timeline entry for Problem deletion before deleting
        try await CarePlanTimelineService.createProblemTimeline(
            operation: .deleted,
            problem: problem,
            on: req.db,
            creatorID: user,
            memberID: carePlan.$member.id
        )

        try await problem.delete(on: req.db)
        return .noContent
    }

    func updateCareTeamMember(req: Request) async throws -> CareTeamMember {
        let id = try req.parameters.require("memberID", as: UUID.self)
        let input = try req.content.decode(CareTeamMemberCreateRequest.self)
        let user = try await TokenController.userIdFromToken(req: req)
        guard let member = try await CareTeamMember.find(id, on: req.db) else {
            throw Abort(.notFound)
        }

        // Get the care plan with member information to access memberID
        guard let carePlan = try await CarePlan.query(on: req.db)
            .filter(\.$id == member.$carePlan.id)
            .with(\.$member)
            .first() else {
            throw Abort(.notFound, reason: "CarePlan not found for care team member")
        }

        member.userID = input.userID
        member.name = input.name
        member.role = input.role
        member.contactInfo = input.contactInfo
        try await member.update(on: req.db)

        // Create timeline entry for CareTeamMember update
        try await CarePlanTimelineService.createCareTeamMemberTimeline(
            operation: .updated,
            member: member,
            on: req.db,
            creatorID: user,
            memberID: carePlan.$member.id
        )

        return member
    }

    func deleteCareTeamMember(req: Request) async throws -> HTTPStatus {
        let id = try req.parameters.require("memberID", as: UUID.self)
        let user = try await TokenController.userIdFromToken(req: req)
        guard let member = try await CareTeamMember.find(id, on: req.db) else {
            throw Abort(.notFound)
        }

        // Get the care plan with member information to access memberID
        guard let carePlan = try await CarePlan.query(on: req.db)
            .filter(\.$id == member.$carePlan.id)
            .with(\.$member)
            .first() else {
            throw Abort(.notFound, reason: "CarePlan not found for care team member")
        }

        // Create timeline entry for CareTeamMember deletion before deleting
        try await CarePlanTimelineService.createCareTeamMemberTimeline(
            operation: .deleted,
            member: member,
            on: req.db,
            creatorID: user,
            memberID: carePlan.$member.id
        )

        try await member.delete(on: req.db)
        return .noContent
    }

    func bulkDeleteCareTeamMembers(req: Request) async throws -> HTTPStatus {
        let carePlanID = try req.parameters.require("carePlanID", as: UUID.self)
        let user = try await TokenController.userIdFromToken(req: req)

        // Validate that the CarePlan exists
        guard let carePlan = try await CarePlan.find(carePlanID, on: req.db) else {
            throw Abort(.notFound, reason: "CarePlan with ID \(carePlanID) not found")
        }

        // Get all care team members for this care plan
        let careTeamMembers = try await CareTeamMember.query(on: req.db)
            .filter(\.$carePlan.$id == carePlanID)
            .all()

        // Delete all care team members
        for member in careTeamMembers {
            try await member.delete(on: req.db)
        }

        // Create a single timeline entry for the bulk deletion
        let memberID = carePlan.$member.id
        let timelineItem = TimelineItem(
            carepackageID: "careplan-\(carePlanID.uuidString)",
            status: "care_team_removed",
            desc: "Care team removed from plan",
            title: "Care Team Removed",
            memberId: memberID,
            visible: true,
            meta: MetaData(data: [
                "ref_id": carePlanID.uuidString,
                "entity_type": "CareTeam",
                "operation": "bulk_deleted",
                "care_plan_id": carePlanID.uuidString,
                "members_count": "\(careTeamMembers.count)"
            ])
        )

        timelineItem.$carePlan.id = carePlanID
        timelineItem.$creator.id = user
        try await timelineItem.save(on: req.db)

        return .noContent
    }

    func updateCarePlanReview(req: Request) async throws -> CarePlanReview {
        let id = try req.parameters.require("reviewID", as: UUID.self)
        let input = try req.content.decode(CarePlanReviewCreateRequest.self)
        let user = try await TokenController.userIdFromToken(req: req)
        guard let review = try await CarePlanReview.find(id, on: req.db) else {
            throw Abort(.notFound)
        }

        // Get the care plan with member information to access memberID
        guard let carePlan = try await CarePlan.query(on: req.db)
            .filter(\.$id == review.$carePlan.id)
            .with(\.$member)
            .first() else {
            throw Abort(.notFound, reason: "CarePlan not found for review")
        }

        review.title = input.title
        review.reviewDate = input.reviewDate
        review.notes = input.notes
        review.reviewerName = input.reviewerName
        review.reviewerRole = input.reviewerRole
        try await review.update(on: req.db)

        // Create timeline entry for CarePlanReview update
        try await CarePlanTimelineService.createCarePlanReviewTimeline(
            operation: .updated,
            review: review,
            on: req.db,
            creatorID: user,
            memberID: carePlan.$member.id
        )

        return review
    }

    func deleteCarePlanReview(req: Request) async throws -> HTTPStatus {
        let id = try req.parameters.require("reviewID", as: UUID.self)
        let user = try await TokenController.userIdFromToken(req: req)
        guard let review = try await CarePlanReview.find(id, on: req.db) else {
            throw Abort(.notFound)
        }

        // Get the care plan with member information to access memberID
        guard let carePlan = try await CarePlan.query(on: req.db)
            .filter(\.$id == review.$carePlan.id)
            .with(\.$member)
            .first() else {
            throw Abort(.notFound, reason: "CarePlan not found for review")
        }

        // Create timeline entry for CarePlanReview deletion before deleting
        try await CarePlanTimelineService.createCarePlanReviewTimeline(
            operation: .deleted,
            review: review,
            on: req.db,
            creatorID: user,
            memberID: carePlan.$member.id
        )

        try await review.delete(on: req.db)
        return .noContent
    }

    // MARK: - Timeline Items

    func createTimelineItem(req: Request) async throws -> TimelineItem {
        let input = try req.content.decode(TimelineItemCreateRequest.self)
        let carePlanID = try req.parameters.require("carePlanID", as: UUID.self)
        let user = try await TokenController.userIdFromToken(req: req)
        
        let timelineItem = TimelineItem()
        timelineItem.$carePlan.id = carePlanID
        timelineItem.carepackageID = input.carepackageID
        timelineItem.title = input.title
        timelineItem.status = input.status
        timelineItem.desc = input.desc
        timelineItem.visible = input.visible
        timelineItem.memberId = input.memberId
        timelineItem.meta = input.meta
        timelineItem.$creator.id = user

        try await timelineItem.save(on: req.db)
        return timelineItem
    }

    func listTimelineItems(req: Request) async throws -> [TimelineItem] {
        let carePlanID = try req.parameters.require("carePlanID", as: UUID.self)
        return try await TimelineItem.query(on: req.db)
            .filter(\.$carePlan.$id == carePlanID)
            .sort(\.$createdAt, .descending)
            .all()
    }

    func getTimelineItem(req: Request) async throws -> TimelineItem {
        let id = try req.parameters.require("timelineItemID", as: UUID.self)
        guard let timelineItem = try await TimelineItem.find(id, on: req.db) else {
            throw Abort(.notFound)
        }
        return timelineItem
    }

    func updateTimelineItem(req: Request) async throws -> TimelineItem {
        let id = try req.parameters.require("timelineItemID", as: UUID.self)
        let input = try req.content.decode(TimelineItemCreateRequest.self)
        guard let timelineItem = try await TimelineItem.find(id, on: req.db) else {
            throw Abort(.notFound)
        }

        timelineItem.carepackageID = input.carepackageID
        timelineItem.title = input.title
        timelineItem.status = input.status
        timelineItem.desc = input.desc
        timelineItem.visible = input.visible
        timelineItem.memberId = input.memberId
        timelineItem.meta = input.meta

        try await timelineItem.update(on: req.db)
        return timelineItem
    }

    func deleteTimelineItem(req: Request) async throws -> HTTPStatus {
        let id = try req.parameters.require("timelineItemID", as: UUID.self)
        guard let timelineItem = try await TimelineItem.find(id, on: req.db) else {
            throw Abort(.notFound)
        }
        try await timelineItem.delete(on: req.db)
        return .noContent
    }

    // MARK: - Note Operations

    func createNote(req: Request) async throws -> Note {
        let input = try req.content.decode(NoteCreateRequest.self)
        let carePlanID = try req.parameters.require("carePlanID", as: UUID.self)
        let user = try await TokenController.userIdFromToken(req: req)

        // Validate that the CarePlan exists and get member information
        guard let carePlan = try await CarePlan.query(on: req.db)
            .filter(\.$id == carePlanID)
            .with(\.$member)
            .first() else {
            throw Abort(.notFound, reason: "CarePlan with ID \(carePlanID) not found")
        }

        let note = Note()
        note.$carePlan.id = carePlanID
        note.$creator.id = user
        note.title = input.title
        note.type = input.type ?? "careplan"
        note.subtitle = input.subtitle
        note.msg = input.msg
        note.status = input.status ?? "active"

        try await note.save(on: req.db)

        // Create timeline entry for Note creation
        try await CarePlanTimelineService.createNoteTimeline(
            operation: .created,
            note: note,
            on: req.db,
            creatorID: user,
            memberID: carePlan.$member.id
        )

        return note
    }

    func listNotes(req: Request) async throws -> [Note] {
        let carePlanID = try req.parameters.require("carePlanID", as: UUID.self)
        return try await Note.query(on: req.db)
            .filter(\.$carePlan.$id == carePlanID)
            .sort(\.$createdAt, .descending)
            .all()
    }

    func getNote(req: Request) async throws -> Note {
        let id = try req.parameters.require("noteID", as: UUID.self)
        guard let note = try await Note.query(on: req.db)
            .filter(\.$id == id)
            .with(\.$creator)
            .with(\.$carePlan, { carePlan in
                carePlan.with(\.$member)
            })
            .first() else {
            throw Abort(.notFound, reason: "Careplan Note with ID \(id) not found")
        }
        return note
    }

    func updateNote(req: Request) async throws -> Note {
        let id = try req.parameters.require("noteID", as: UUID.self)
        let input = try req.content.decode(NoteUpdateRequest.self)
        let user = try await TokenController.userIdFromToken(req: req)

        guard let note = try await Note.find(id, on: req.db) else {
            throw Abort(.notFound)
        }

        // Get the care plan with member information to access memberID
        guard let carePlan = try await CarePlan.query(on: req.db)
            .filter(\.$id == note.$carePlan.id!)
            .with(\.$member)
            .first() else {
            throw Abort(.notFound, reason: "CarePlan not found for note")
        }

        note.title = input.title
        note.type = input.type ?? note.type
        note.subtitle = input.subtitle
        note.msg = input.msg
        note.status = input.status ?? note.status

        try await note.update(on: req.db)

        // Create timeline entry for Note update
        try await CarePlanTimelineService.createNoteTimeline(
            operation: .updated,
            note: note,
            on: req.db,
            creatorID: user,
            memberID: carePlan.$member.id
        )

        return note
    }

    func deleteNote(req: Request) async throws -> HTTPStatus {
        let id = try req.parameters.require("noteID", as: UUID.self)
        let user = try await TokenController.userIdFromToken(req: req)

        guard let note = try await Note.find(id, on: req.db) else {
            throw Abort(.notFound)
        }

        // Get the care plan with member information to access memberID
        guard let carePlan = try await CarePlan.query(on: req.db)
            .filter(\.$id == note.$carePlan.id!)
            .with(\.$member)
            .first() else {
            throw Abort(.notFound, reason: "CarePlan not found for note")
        }

        // Create timeline entry for Note deletion before deleting
        try await CarePlanTimelineService.createNoteTimeline(
            operation: .deleted,
            note: note,
            on: req.db,
            creatorID: user,
            memberID: carePlan.$member.id
        )

        try await note.delete(on: req.db)
        return .noContent
    }

    // MARK: - SMS Reminder Scheduling for Goals

    private func scheduleGoalReminders(req: Request, goal: Goal, member: Member) async throws {
        // Only schedule reminders if we have a valid target date and phone number
        guard let goalId = goal.id,
              let phoneNumber = member.smsPhone(),
              !phoneNumber.isEmpty else {
            req.logger.info("Skipping SMS reminder scheduling for goal - missing required data")
            return
        }

        let targetDate = goal.targetDate
        let memberName = member.fullName()
        let goalTitle = goal.title

        do {
            _ = try await req.smsReminderScheduler.scheduleGoalReminder(
                req: req,
                goalId: goalId,
                phoneNumber: phoneNumber,
                targetDate: targetDate,
                memberName: memberName,
                goalTitle: goalTitle
            ).get()
        } catch {
            req.logger.error("Failed to schedule SMS reminders for goal \(goalId): \(error)")
            // Don't fail the goal creation if reminder scheduling fails
        }
    }

    private func handleGoalReminderUpdate(req: Request, goal: Goal, member: Member) async throws {
        let isCompleted = goal.status.lowercased() == "completed" || goal.status.lowercased() == "complete"

        if isCompleted {
            // Cancel reminders if goal is completed
            try await cancelGoalReminders(req: req, goalId: goal.id!)
        } else {
            // Reschedule reminders if target date changed
            try await scheduleGoalReminders(req: req, goal: goal, member: member)
        }
    }

    private func cancelGoalReminders(req: Request, goalId: UUID) async throws {
        do {
            _ = try await req.smsReminderScheduler.cancelReminders(req: req, entityId: goalId, entityType: .goalTarget).get()
        } catch {
            req.logger.error("Failed to cancel SMS reminders for goal \(goalId): \(error)")
            // Don't fail the operation if reminder cancellation fails
        }
    }

    // MARK: - SMS Reminder Scheduling for Interventions

    private func scheduleInterventionReminders(req: Request, intervention: Intervention, member: Member) async throws {
        // Only schedule reminders if we have a valid due date and phone number
        guard let interventionId = intervention.id,
              let phoneNumber = member.smsPhone(),
              !phoneNumber.isEmpty else {
            req.logger.info("Skipping SMS reminder scheduling for intervention - missing required data")
            return
        }

        let dueDate = intervention.dueDate
        let memberName = member.fullName()
        let interventionAction = intervention.action

        do {
            _ = try await req.smsReminderScheduler.scheduleInterventionReminder(
                req: req,
                interventionId: interventionId,
                phoneNumber: phoneNumber,
                dueDate: dueDate,
                memberName: memberName,
                interventionAction: interventionAction
            ).get()
        } catch {
            req.logger.error("Failed to schedule SMS reminders for intervention \(interventionId): \(error)")
            // Don't fail the intervention creation if reminder scheduling fails
        }
    }

    private func handleInterventionReminderUpdate(req: Request, intervention: Intervention, member: Member) async throws {
        let isCompleted = intervention.status?.lowercased() == "completed" || intervention.status?.lowercased() == "complete"

        if isCompleted {
            // Cancel reminders if intervention is completed
            try await cancelInterventionReminders(req: req, interventionId: intervention.id!)
        } else {
            // Reschedule reminders if due date changed
            try await scheduleInterventionReminders(req: req, intervention: intervention, member: member)
        }
    }

    private func cancelInterventionReminders(req: Request, interventionId: UUID) async throws {
        do {
            _ = try await req.smsReminderScheduler.cancelReminders(req: req, entityId: interventionId, entityType: .interventionDue).get()
        } catch {
            req.logger.error("Failed to cancel SMS reminders for intervention \(interventionId): \(error)")
            // Don't fail the operation if reminder cancellation fails
        }
    }

    // MARK: - SMS Reminder Scheduling for Follow-ups

    private func scheduleFollowUpReminders(req: Request, followUp: CarePlanFollowUp, member: Member) async throws {
        // Only schedule reminders if we have a valid follow-up date and phone number
        guard let followUpId = followUp.id,
              let phoneNumber = member.smsPhone(),
              !phoneNumber.isEmpty else {
            req.logger.info("Skipping SMS reminder scheduling for follow-up - missing required data")
            return
        }

        let followUpDate = followUp.datetime
        let memberName = member.fullName()
        let followUpType = followUp.type

        do {
            _ = try await req.smsReminderScheduler.scheduleFollowUpReminder(
                req: req,
                followUpId: followUpId,
                phoneNumber: phoneNumber,
                followUpDate: followUpDate,
                memberName: memberName,
                followUpType: followUpType
            ).get()
        } catch {
            req.logger.error("Failed to schedule SMS reminders for follow-up \(followUpId): \(error)")
            // Don't fail the follow-up creation if reminder scheduling fails
        }
    }

    private func handleFollowUpReminderUpdate(req: Request, followUp: CarePlanFollowUp, member: Member) async throws {
        let isCompleted = followUp.outcome.lowercased().contains("completed") || followUp.outcome.lowercased().contains("reached")

        if isCompleted {
            // Cancel reminders if follow-up is completed
            try await cancelFollowUpReminders(req: req, followUpId: followUp.id!)
        } else {
            // Reschedule reminders if follow-up date changed
            try await scheduleFollowUpReminders(req: req, followUp: followUp, member: member)
        }
    }

    private func cancelFollowUpReminders(req: Request, followUpId: UUID) async throws {
        do {
            _ = try await req.smsReminderScheduler.cancelReminders(req: req, entityId: followUpId, entityType: .followUp).get()
        } catch {
            req.logger.error("Failed to cancel SMS reminders for follow-up \(followUpId): \(error)")
            // Don't fail the operation if reminder cancellation fails
        }
    }
}


final class CarePlan: Model, @unchecked Sendable {
    static let schema = "care_plans"

    @ID var id: UUID?
    @Parent(key: "member_id") var member: Member

    @Field(key: "title") var title: String
    @Field(key: "start_date") var startDate: Date
    @Field(key: "last_reviewed") var lastReviewed: Date?
    @Field(key: "next_review_date") var nextReviewDate: Date?
    @Field(key: "outcome") var outcome: String?
    @Field(key: "status") var status: String

    @Timestamp(key: "created_at", on: .create) var createdAt: Date?
    @Timestamp(key: "updated_at", on: .update) var updatedAt: Date?

    @Children(for: \.$carePlan) var goals: [Goal]
    @Children(for: \.$carePlan) var interventions: [Intervention]
    @Children(for: \.$carePlan) var problems: [Problem]
    @Children(for: \.$carePlan) var teamMembers: [CareTeamMember]
    @Children(for: \.$carePlan) var reviews: [CarePlanReview]
    @Children(for: \.$carePlan) var followUps: [CarePlanFollowUp]
    @Children(for: \.$carePlan) var services: [CarePlanService]
    @Children(for: \.$carePlan) var notes: [Note]
    @Children(for: \.$carePlan) var timelineItems: [TimelineItem]
}

// MARK: - CarePlan Content Conformance
extension CarePlan: Content {
    enum CodingKeys: String, CodingKey {
        case id
        case title
        case startDate = "start_date"
        case lastReviewed = "last_reviewed"
        case nextReviewDate = "next_review_date"
        case outcome
        case status
        case createdAt = "created_at"
        case updatedAt = "updated_at"
        // Note: member is excluded from coding keys to prevent decoding issues
    }
}


struct CreateCarePlan: Migration {
    func prepare(on database: Database) -> EventLoopFuture<Void> {
        database.schema("care_plans")
            .id()
            .field("member_id", .uuid, .required, .references("members", "id", onDelete: .cascade))
            .field("start_date", .date, .required)
            .field("last_reviewed", .date)
            .field("next_review_date", .date)
            .field("outcome", .string)
            .field("status", .string, .required)
            .field("created_at", .datetime)
            .field("updated_at", .datetime)
            .create()
    }

    func revert(on database: Database) -> EventLoopFuture<Void> {
        database.schema("care_plans").delete()
    }
}


final class Goal: Model, @unchecked Sendable {
    static let schema = "goals"

    @ID var id: UUID?
    @Parent(key: "care_plan_id") var carePlan: CarePlan

    @Field(key: "title") var title: String
    @Field(key: "description") var description: String
     @Field(key: "type") var type: String
     @Field(key: "target_date") var targetDate: Date
     @Field(key: "status") var status: String
     @Field(key: "outcome") var outcome: String?

     @Field(key: "objective") var objective: String
     @Field(key: "measurement_criteria") var measurementCriteria: String
     @Field(key: "achievability_note") var achievabilityNote: String?
     @Field(key: "barriers") var barriers: String?

    @Timestamp(key: "created_at", on: .create) var createdAt: Date?
    @Timestamp(key: "updated_at", on: .update) var updatedAt: Date?
}

// MARK: - Goal Content Conformance
extension Goal: Content {
    enum CodingKeys: String, CodingKey {
        case id
        case title
        case description
        case type
        case targetDate = "target_date"
        case status
        case outcome
        case objective
        case measurementCriteria = "measurement_criteria"
        case achievabilityNote = "achievability_note"
        case barriers
        case createdAt = "created_at"
        case updatedAt = "updated_at"
        // Note: carePlan is excluded from coding keys to prevent decoding issues
    }
}

struct CreateGoal: Migration {
    func prepare(on database: Database) -> EventLoopFuture<Void> {
        database.schema("goals")
            .id()
            .field("care_plan_id", .uuid, .required, .references("care_plans", "id", onDelete: .cascade))
            .field("description", .string, .required)
            .field("type", .string, .required)
            .field("target_date", .date, .required)
            .field("status", .string, .required)
            .field("outcome", .string)
            .field("objective", .string, .required)
            .field("measurement_criteria", .string, .required)
            .field("achievability_note", .string)
            .field("barriers", .string)
            .field("created_at", .datetime)
            .field("updated_at", .datetime)
            .create()
    }

    func revert(on database: Database) -> EventLoopFuture<Void> {
        database.schema("goals").delete()
    }
}


final class Intervention: Model, @unchecked Sendable {
    static let schema = "interventions"

    @ID var id: UUID?
    @Parent(key: "care_plan_id") var carePlan: CarePlan

    @OptionalField(key: "title") var title: String?
    @Field(key: "action") var action: String
    @Field(key: "responsible_party") var responsibleParty: String
    @OptionalField(key: "responsible_party_id") var responsiblePartyId: String?
    @OptionalField(key: "status") var status: String?
    @Field(key: "due_date") var dueDate: Date
    @OptionalField(key: "note") var note: String?

    @Timestamp(key: "created_at", on: .create) var createdAt: Date?
    @Timestamp(key: "updated_at", on: .update) var updatedAt: Date?
}

// MARK: - Intervention Content Conformance
extension Intervention: Content {
    enum CodingKeys: String, CodingKey {
        case id
        case title
        case action
        case responsibleParty = "responsible_party"
        case responsiblePartyId = "responsible_party_id"
        case status
        case dueDate = "due_date"
        case note
        case createdAt = "created_at"
        case updatedAt = "updated_at"
        // Note: carePlan is excluded from coding keys to prevent decoding issues
    }
}

struct CreateIntervention: Migration {
    func prepare(on database: Database) -> EventLoopFuture<Void> {
        database.schema("interventions")
            .id()
            .field("care_plan_id", .uuid, .required, .references("care_plans", "id", onDelete: .cascade))
            .field("action", .string, .required)
            .field("responsible_party", .string, .required)
            .field("due_date", .date, .required)
            .field("created_at", .datetime)
            .field("updated_at", .datetime)
            .create()
    }

    func revert(on database: Database) -> EventLoopFuture<Void> {
        database.schema("interventions").delete()
    }
}


final class Problem: Model, @unchecked Sendable {
    static let schema = "problems"

    @ID var id: UUID?
    @OptionalParent(key: "care_plan_id") var carePlan: CarePlan?

    @Siblings(through: MemberProblems.self, from: \.$problem, to: \.$member)
    var members: [Member]

    @OptionalField(key: "title") var title: String?
    @Field(key: "icd_code") var icdCode: String? //"I10",
    @Field(key: "description") var description: String //"Essential (primary) hypertension",
    @Field(key: "clinical_note") var clinicalNote: String? //"BP remains elevated despite medication adjustment.",
    @Field(key: "status") var status: String // active | resolved | inactive
    @Field(key: "date_identified") var dateIdentified: Date
    @Field(key: "source") var source: String // EHR import | self-reported | care team
    @Field(key: "confirmed_by") var confirmedBy: String? //"Dr. Smith, MD"

    @Timestamp(key: "created_at", on: .create) var createdAt: Date?
    @Timestamp(key: "updated_at", on: .update) var updatedAt: Date?
}

// MARK: - Problem Content Conformance
extension Problem: Content {
    enum CodingKeys: String, CodingKey {
        case id
        case title
        case icdCode = "icd_code"
        case description
        case clinicalNote = "clinical_note"
        case status
        case dateIdentified = "date_identified"
        case source
        case confirmedBy = "confirmed_by"
        case createdAt = "created_at"
        case updatedAt = "updated_at"
        // Note: carePlan is excluded from coding keys to prevent decoding issues
    }
}

struct CreateProblem: Migration {
    func prepare(on database: Database) -> EventLoopFuture<Void> {
        database.schema("problems")
            .id()
            .field("care_plan_id", .uuid, .required, .references("care_plans", "id", onDelete: .cascade))
            .field("description", .string, .required)
            .field("icd_code", .string)
            .field("created_at", .datetime)
            .field("updated_at", .datetime)
            .create()
    }

    func revert(on database: Database) -> EventLoopFuture<Void> {
        database.schema("problems").delete()
    }
}

struct CreateProblemUpdate: Migration {
    func prepare(on database: Database) -> EventLoopFuture<Void> {
        database.schema("problems")
            .field("clinical_note", .string)
            .field("status", .string, .required, .sql(.default("active")))
            .field("date_identified", .date, .required)
            .field("source", .string, .required, .sql(.default("care team")))
            .field("confirmed_by", .string)
            .update()
    }

    func revert(on database: Database) -> EventLoopFuture<Void> {
        database.schema("problems")
            .deleteField("clinical_note")
            .deleteField("status")
            .deleteField("date_identified")
            .deleteField("source")
            .deleteField("confirmed_by")
            .update()
    }
}

struct MakeCarePlanIdOptionalInProblems: Migration {
    func prepare(on database: Database) -> EventLoopFuture<Void> {
        // Note: This migration makes care_plan_id optional to allow member-specific problems
        // The model change from @Parent to @OptionalParent handles this at the application level
        return database.eventLoop.makeSucceededFuture(())
    }

    func revert(on database: Database) -> EventLoopFuture<Void> {
        // Revert is handled by changing the model back to @Parent
        return database.eventLoop.makeSucceededFuture(())
    }
}


final class CareTeamMember: Model, @unchecked Sendable {
    static let schema = "care_team_members"

    @ID var id: UUID?
    @Parent(key: "care_plan_id") var carePlan: CarePlan

    @Field(key: "user_id") var userID: UUID?
    @Field(key: "name") var name: String
    @Field(key: "role") var role: String
    @Field(key: "contact_info") var contactInfo: String
    
    @Timestamp(key: "created_at", on: .create) var createdAt: Date?
    @Timestamp(key: "updated_at", on: .update) var updatedAt: Date?
}

// MARK: - CareTeamMember Content Conformance
extension CareTeamMember: Content {
    enum CodingKeys: String, CodingKey {
        case id
        case userID = "user_id"
        case name
        case role
        case contactInfo = "contact_info"
        case createdAt = "created_at"
        case updatedAt = "updated_at"
        // Note: carePlan is excluded from coding keys to prevent decoding issues
    }
}

struct CreateCareTeamMember: Migration {
    func prepare(on database: Database) -> EventLoopFuture<Void> {
        database.schema("care_team_members")
            .id()
            .field("care_plan_id", .uuid, .required, .references("care_plans", "id", onDelete: .cascade))
            .field("user_id", .uuid)
            .field("name", .string, .required)
            .field("role", .string, .required)
            .field("contact_info", .string, .required)
            .field("created_at", .datetime)
            .field("updated_at", .datetime)
            .create()
    }

    func revert(on database: Database) -> EventLoopFuture<Void> {
        database.schema("care_team_members").delete()
    }
}


final class CarePlanReview: Model, @unchecked Sendable {
    static let schema = "care_plan_reviews"

    @ID var id: UUID?
    @Parent(key: "care_plan_id") var carePlan: CarePlan

    @OptionalField(key: "title") var title: String?
    @Field(key: "review_date") var reviewDate: Date
    @Field(key: "notes") var notes: String?
    @Field(key: "reviewer_name") var reviewerName: String
    @Field(key: "reviewer_role") var reviewerRole: String?

    @Timestamp(key: "created_at", on: .create) var createdAt: Date?
    @Timestamp(key: "updated_at", on: .update) var updatedAt: Date?
}

// MARK: - CarePlanReview Content Conformance
extension CarePlanReview: Content {
    enum CodingKeys: String, CodingKey {
        case id
        case title
        case reviewDate = "review_date"
        case notes
        case reviewerName = "reviewer_name"
        case reviewerRole = "reviewer_role"
        case createdAt = "created_at"
        case updatedAt = "updated_at"
        // Note: carePlan is excluded from coding keys to prevent decoding issues
    }
}

struct CreateCarePlanReview: Migration {
    func prepare(on database: Database) -> EventLoopFuture<Void> {
        database.schema("care_plan_reviews")
            .id()
            .field("care_plan_id", .uuid, .required, .references("care_plans", "id", onDelete: .cascade))
            .field("title", .string)
            .field("review_date", .date, .required)
            .field("notes", .string)
            .field("reviewer_name", .string, .required)
            .field("reviewer_role", .string)
            .field("created_at", .datetime)
            .field("updated_at", .datetime)
            .create()
    }

    func revert(on database: Database) -> EventLoopFuture<Void> {
        database.schema("care_plan_reviews").delete()
    }
}


final class CarePlanService: Model, @unchecked Sendable {
    static let schema = "care_plan_services"

    @ID var id: UUID?
    @Parent(key: "care_plan_id") var carePlan: CarePlan

    @Field(key: "cbo_name") var cboName: String
    @Field(key: "staff_name") var staffName: String
    @Field(key: "added_by") var addedBy: String
    @Field(key: "status") var status: String // pending, booked, completed, no_show
    @Field(key: "appointment_date") var appointmentDate: Date?
    @Field(key: "outcome_reason_type") var outcomeReasonType: String?
    @Field(key: "outcome_reason_description") var outcomeReasonDescription: String?
    @OptionalField(key: "ref_id") var refId: String?

    @Timestamp(key: "created_at", on: .create) var createdAt: Date?
    @Timestamp(key: "updated_at", on: .update) var updatedAt: Date?
}

// MARK: - CarePlanService Content Conformance
extension CarePlanService: Content {
    enum CodingKeys: String, CodingKey {
        case id
        case cboName = "cbo_name"
        case staffName = "staff_name"
        case addedBy = "added_by"
        case status
        case appointmentDate = "appointment_date"
        case outcomeReasonType = "outcome_reason_type"
        case outcomeReasonDescription = "outcome_reason_description"
        case refId = "ref_id"
        case createdAt = "created_at"
        case updatedAt = "updated_at"
        // Note: carePlan is excluded from coding keys to prevent decoding issues
    }
}

struct CreateCarePlanService: Migration {
    func prepare(on database: Database) -> EventLoopFuture<Void> {
        database.schema("care_plan_services")
            .id()
            .field("care_plan_id", .uuid, .required, .references("care_plans", "id", onDelete: .cascade))
            .field("cbo_name", .string, .required)
            .field("staff_name", .string, .required)
            .field("added_by", .string, .required)
            .field("status", .string, .required)
            .field("appointment_date", .datetime)
            .field("outcome_reason_type", .string)
            .field("outcome_reason_description", .string)
            .field("ref_id", .string)
            .field("created_at", .datetime)
            .field("updated_at", .datetime)
            .create()
    }

    func revert(on database: Database) -> EventLoopFuture<Void> {
        database.schema("care_plan_services").delete()
    }
}


final class CarePlanFollowUp: Model, @unchecked Sendable {
    static let schema = "care_plan_follow_ups"

    @ID var id: UUID?
    @Parent(key: "care_plan_id") var carePlan: CarePlan

    @Field(key: "datetime") var datetime: Date
    @Field(key: "type") var type: String // Phone, In-Person, Email, etc.
    @Field(key: "outcome") var outcome: String // Reached, No Answer, Rescheduled, etc.
    @Field(key: "notes") var notes: String?
    @Field(key: "staff_name") var staffName: String
    @Field(key: "staff_role") var staffRole: String?
    
    @Timestamp(key: "created_at", on: .create) var createdAt: Date?
    @Timestamp(key: "updated_at", on: .update) var updatedAt: Date?
}

// MARK: - CarePlanFollowUp Content Conformance
extension CarePlanFollowUp: Content {
    enum CodingKeys: String, CodingKey {
        case id
        case datetime
        case type
        case outcome
        case notes
        case staffName = "staff_name"
        case staffRole = "staff_role"
        case createdAt = "created_at"
        case updatedAt = "updated_at"
        // Note: carePlan is excluded from coding keys to prevent decoding issues
    }
}

struct CreateCarePlanFollowUp: Migration {
    func prepare(on database: Database) -> EventLoopFuture<Void> {
        database.schema("care_plan_follow_ups")
            .id()
            .field("care_plan_id", .uuid, .required, .references("care_plans", "id", onDelete: .cascade))
            .field("datetime", .datetime, .required)
            .field("type", .string, .required)
            .field("outcome", .string, .required)
            .field("notes", .string)
            .field("staff_name", .string, .required)
            .field("staff_role", .string)
            .field("created_at", .datetime)
            .field("updated_at", .datetime)
            .create()
    }

    func revert(on database: Database) -> EventLoopFuture<Void> {
        database.schema("care_plan_follow_ups").delete()
    }
}

// MARK: - Request DTOs
struct CarePlanCreateRequest: Content {
    let title: String
    let startDate: Date
    let lastReviewed: Date?
    let nextReviewDate: Date?
    let outcome: String?
    let status: String
}

struct GoalCreateRequest: Content {
    let title: String
    let description: String
    let type: String
    let targetDate: Date
    let status: String
    let outcome: String?
    let objective: String
    let measurementCriteria: String
    let achievabilityNote: String?
    let barriers: String?
}

struct InterventionCreateRequest: Content {
    let title: String?
    let action: String
    let responsibleParty: String
    let responsiblePartyId: String?
    let status: String?
    let dueDate: Date
    let note: String?
    let autoAssignTask: Bool?

    var shouldAutoAssignTask: Bool {
        return autoAssignTask ?? false
    }
}

struct ProblemCreateRequest: Content {
    let title: String?
    let icdCode: String?
    let description: String
    let clinicalNote: String?
    let status: String
    let dateIdentified: Date
    let source: String
    let confirmedBy: String?
}

struct CareTeamMemberCreateRequest: Content {
    let userID: UUID?
    let name: String
    let role: String
    let contactInfo: String
}

struct AddTeamMembersRequest: Content {
    let teamID: UUID
}

// MARK: - Response DTOs
struct CarePlanWithCountsResponse: Content {
    let carePlan: CarePlan
    let goalsCount: Int
    let interventionsCount: Int
}

struct CarePlansListResponse: Content {
    let carePlans: [CarePlanWithCountsResponse]
}

struct CarePlanReviewCreateRequest: Content {
    let title: String?
    let reviewDate: Date
    let notes: String?
    let reviewerName: String
    let reviewerRole: String?
}

struct TimelineItemCreateRequest: Content {
    let carepackageID: String
    let title: String?
    let status: String
    let desc: String
    let visible: Bool?
    let memberId: UUID?
    let meta: MetaData?
}

struct CarePlanServiceCreateRequest: Content {
    let cboName: String
    let staffName: String
    let addedBy: String
    let status: String
    let appointmentDate: Date?
    let outcomeReasonType: String?
    let outcomeReasonDescription: String?
    let refId: String?
}

struct CarePlanFollowUpCreateRequest: Content {
    let datetime: Date
    let type: String
    let outcome: String?
    let notes: String?
    let staffName: String
    let staffRole: String?
}

struct NoteCreateRequest: Content {
    let title: String
    let type: String?
    let subtitle: String?
    let msg: String
    let status: String?
}

struct NoteUpdateRequest: Content {
    let title: String
    let type: String?
    let subtitle: String?
    let msg: String
    let status: String?
}

struct TokenController {
    
    static func userFromToken(req: Request) async throws -> User {
        let token = try extractBearerToken(from: req)
        return try await lookupUser(token: token, req: req)
    }
    
    static func userIdFromToken(req: Request) async throws -> UUID {
        let token = try extractBearerToken(from: req)
        return try await lookup(token: token, req: req)
    }
    
    static func lookup(token:String, req: Request) async throws -> UUID {
        guard let token = try await Token.query(on: req.db).filter(\.$value == token).with(\.$user).first() else {
            throw Abort(.notFound)
        }
        
        guard let id = token.user.id?.uuidString else {
            throw Abort(.notFound)
        }
        
        guard let user = try await User.query(on: req.db)
            .filter(\.$auth == id)
            .with(\.$org)
            .with(\.$teams)
            .with(\.$attachments).first() else {
            throw Abort(.notFound)
        }
        
        return try user.requireID()
    }
    
    static func lookupUser(token:String, req: Request) async throws -> User {
        guard let token = try await Token.query(on: req.db).filter(\.$value == token).with(\.$user).first() else {
            throw Abort(.notFound)
        }
        
        guard let id = token.user.id?.uuidString else {
            throw Abort(.notFound)
        }
        
        guard let user = try await User.query(on: req.db)
            .filter(\.$auth == id)
            .with(\.$org)
            .with(\.$teams)
            .with(\.$attachments).first() else {
            throw Abort(.notFound)
        }
        
        return user
    }
}
